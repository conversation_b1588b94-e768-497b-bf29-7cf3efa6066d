"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupButton,
  InputGroupInput,
} from "@/components/ui/input-group";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { ChevronDownIcon } from "lucide-react";
import type {
  Attributes,
  AttributeValues,
  AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import type { Supplier } from "@/lib/api/suppliers/models";
import MatrixAttributes from "./create/AttributesSection";
import type { Category } from "@/lib/api/categories/models";
import type { Brand } from "@/lib/api/brands/models";
import type { AttributeConfig } from "./create/AttributesSection";
import type { ItemMatrixSchema } from "@/lib/api/items/matrices/models";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

const matrixFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  matrix_type: z.string().min(1, "Matrix type is required"),
  description: z.string().optional(),
  category: z.number().optional(),
  brand: z.number().optional(),
  vendor_id: z.number().optional(),
  default_price: z.number().min(0, "Price cannot be negative"),
  default_cost: z.number().min(0, "Cost cannot be negative"),
  msrp: z.number().min(0, "MSRP cannot be negative").optional(),
  has_discount: z.boolean().optional(),
  discount: z.number().min(0, "Discount cannot be negative").max(100, "Discount cannot exceed 100%").optional(),
  vat_included: z.boolean().optional(),
  vat_percentage: z.number().min(0, "VAT cannot be negative").max(100, "VAT cannot exceed 100%").optional(),
});

type MatrixFormData = z.infer<typeof matrixFormSchema>;

interface MatrixFormProps {
  initialData?: ItemMatrixSchema;
  attributes: AttributeConfig[];
  availableAttributes: Attributes[];
  availableAttributeValues: AttributeValues[];
  vendors: Supplier[];
  categories: Category[];
  brands: Brand[];
  storeId: number;
  onSubmit: (data: ItemMatrixSchema, attributes: AttributeConfig[]) => Promise<void>;
  onCancel: () => void;
  onAddAttribute: (attributeId: number) => void;
  onRemoveAttribute: (attributeId: number) => void;
  onToggleValue: (attributeId: number, valueId: number) => void;
  onAddValue: (attributeId: number, value: string) => void;
  onRemoveValue: (attributeId: number, valueId: number) => void;
  onAddBulkValues: (attributeId: number, values: AttributeValuesSchema) => Promise<void>;
  loading: boolean;
  saving: boolean;
}

const matrixTypes = [
  { id: "size", name: "Size Based" },
  { id: "color", name: "Color Based" },
  { id: "combo", name: "Size & Color" },
];

export const MatrixForm: React.FC<MatrixFormProps> = ({
  initialData,
  attributes,
  availableAttributes,
  availableAttributeValues,
  vendors,
  categories,
  brands,
  storeId,
  onSubmit,
  onCancel,
  onAddAttribute,
  onRemoveAttribute,
  onToggleValue,
  onAddValue,
  onRemoveValue,
  onAddBulkValues,
  loading,
  saving
}) => {
  const form = useForm<MatrixFormData>({
    resolver: zodResolver(matrixFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      matrix_type: initialData?.matrix_type || "",
      description: initialData?.description || "",
      category: initialData?.category,
      brand: initialData?.brand,
      vendor_id: initialData?.vendor_id,
      default_price: initialData?.default_price || 0,
      default_cost: initialData?.default_cost || 0,
      msrp: initialData?.msrp || 0,
      has_discount: initialData?.has_discount || false,
      discount: initialData?.discount || 0,
      vat_included: initialData?.vat_included || false,
      vat_percentage: initialData?.vat_percentage || 0,
    },
  });

  const handleSubmit = async (data: MatrixFormData) => {
    await onSubmit({
      ...data,
      store_id: storeId,
      description: data.description || "",
      category: data.category || 0,
      brand: data.brand || 0,
      vendor_id: data.vendor_id || 0,
    }, attributes);
  };

  useEffect(() => {
    if (initialData) {
      form.reset({
        name: initialData.name,
        matrix_type: initialData.matrix_type,
        description: initialData.description || "",
        category: initialData.category,
        brand: initialData.brand,
        vendor_id: initialData.vendor_id,
        default_price: initialData.default_price,
        default_cost: initialData.default_cost,
        msrp: initialData.msrp || 0,
        has_discount: initialData.has_discount || false,
        discount: initialData.discount || 0,
        vat_included: initialData.vat_included || false,
        vat_percentage: initialData.vat_percentage || 0,
      });
    }
  }, [initialData, form]);

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>{initialData ? "Edit Item Matrix" : "Create Item Matrix"}</CardTitle>
      </CardHeader>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <CardContent className="space-y-6 p-4 md:p-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Matrix Name *</Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Enter matrix name"
                  className={form.formState.errors.name ? "border-red-500" : ""}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="matrix_type">Matrix Type *</Label>
                <InputGroup>
                  <InputGroupInput 
                    placeholder="Select matrix type" 
                    value={matrixTypes.find(t => t.id === form.watch("matrix_type"))?.name || ''}
                    readOnly
                    className={form.formState.errors.matrix_type ? "border-red-500" : ""}
                  />
                  <InputGroupAddon align="inline-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                          Select <ChevronDownIcon className="size-3" />
                        </InputGroupButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {matrixTypes.map((type) => (
                          <DropdownMenuItem 
                            key={type.id} 
                            onClick={() => form.setValue("matrix_type", type.id)}
                          >
                            {type.name}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </InputGroupAddon>
                </InputGroup>
                {form.formState.errors.matrix_type && (
                  <p className="text-sm text-red-500">{form.formState.errors.matrix_type.message}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...form.register("description")}
                placeholder="Enter description"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <InputGroup>
                <InputGroupInput 
                  placeholder="Select category" 
                  value={categories.find(c => c.id === form.watch("category"))?.name || ''}
                  readOnly
                />
                <InputGroupAddon align="inline-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                        Select <ChevronDownIcon className="size-3" />
                      </InputGroupButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {categories.map((category) => (
                        <DropdownMenuItem 
                          key={category.id} 
                          onClick={() => form.setValue("category", category.id)}
                        >
                          {category.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </InputGroupAddon>
              </InputGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="brand">Brand</Label>
              <InputGroup>
                <InputGroupInput 
                  placeholder="Select brand" 
                  value={brands.find(b => b.id === form.watch("brand"))?.name || ''}
                  readOnly
                />
                <InputGroupAddon align="inline-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                        Select <ChevronDownIcon className="size-3" />
                      </InputGroupButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {brands.map((brand) => (
                        <DropdownMenuItem 
                          key={brand.id} 
                          onClick={() => form.setValue("brand", brand.id)}
                        >
                          {brand.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </InputGroupAddon>
              </InputGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="vendor">Vendor</Label>
              <InputGroup>
                <InputGroupInput 
                  placeholder="Select vendor" 
                  value={vendors.find(v => v.id === form.watch("vendor_id"))?.name || ''}
                  readOnly
                />
                <InputGroupAddon align="inline-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                        Select <ChevronDownIcon className="size-3" />
                      </InputGroupButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {vendors.map((vendor) => (
                        <DropdownMenuItem 
                          key={vendor.id} 
                          onClick={() => form.setValue("vendor_id", vendor.id)}
                        >
                          {vendor.name}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </InputGroupAddon>
              </InputGroup>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="default_price">Default Price *</Label>
              <Input
                id="default_price"
                type="number"
                step="0.01"
                min="0"
                {...form.register("default_price", { valueAsNumber: true })}
                placeholder="0.00"
                className={form.formState.errors.default_price ? "border-red-500" : ""}
              />
              {form.formState.errors.default_price && (
                <p className="text-sm text-red-500">{form.formState.errors.default_price.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="default_cost">Default Cost *</Label>
              <Input
                id="default_cost"
                type="number"
                step="0.01"
                min="0"
                {...form.register("default_cost", { valueAsNumber: true })}
                placeholder="0.00"
                className={form.formState.errors.default_cost ? "border-red-500" : ""}
              />
              {form.formState.errors.default_cost && (
                <p className="text-sm text-red-500">{form.formState.errors.default_cost.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="msrp">MSRP</Label>
              <Input
                id="msrp"
                type="number"
                step="0.01"
                min="0"
                {...form.register("msrp", { valueAsNumber: true })}
                placeholder="0.00"
                className={form.formState.errors.msrp ? "border-red-500" : ""}
              />
              {form.formState.errors.msrp && (
                <p className="text-sm text-red-500">{form.formState.errors.msrp.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-8 gap-y-6">
            <div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="has_discount"
                  checked={form.watch("has_discount")}
                  onCheckedChange={(checked) => form.setValue("has_discount", checked)}
                />
                <Label htmlFor="has_discount">Enable Discount</Label>
              </div>

              {form.watch("has_discount") && (
                <div className="space-y-2 mt-2">
                  <Label htmlFor="discount">Discount Percentage</Label>
                  <Input
                    id="discount"
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    {...form.register("discount", { valueAsNumber: true })}
                    placeholder="0.0"
                    className={form.formState.errors.discount ? "border-red-500" : ""}
                  />
                  {form.formState.errors.discount && (
                    <p className="text-sm text-red-500">{form.formState.errors.discount.message}</p>
                  )}
                </div>
              )}
            </div>
            
            <div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="vat_included"
                  checked={form.watch("vat_included")}
                  onCheckedChange={(checked) => form.setValue("vat_included", checked)}
                />
                <Label htmlFor="vat_included">VAT Included</Label>
              </div>

              {form.watch("vat_included") && (
                <div className="space-y-2 mt-2">
                  <Label htmlFor="vat_percentage">VAT Percentage (%)</Label>
                  <Input
                    id="vat_percentage"
                    type="number"
                    step="0.01"
                    min="0"
                    max="100"
                    {...form.register("vat_percentage", { valueAsNumber: true })}
                    placeholder="0.00"
                    className={form.formState.errors.vat_percentage ? "border-red-500" : ""}
                  />
                  {form.formState.errors.vat_percentage && (
                    <p className="text-sm text-red-500">{form.formState.errors.vat_percentage.message}</p>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <MatrixAttributes
            attributes={attributes}
            availableAttributes={availableAttributes}
            onAddAttribute={onAddAttribute}
            onRemoveAttribute={onRemoveAttribute}
            onToggleValue={onToggleValue}
            onAddValue={onAddValue}
            onRemoveValue={onRemoveValue}
            storeId={storeId}
            onAddBulkValues={onAddBulkValues}
          />
        </CardContent>

        <CardFooter className="flex justify-end space-x-2 p-4 md:p-6">
          <Button type="button" variant="outline" onClick={onCancel} disabled={saving}>
            Cancel
          </Button>
          <Button type="submit" disabled={saving}>
            {saving ? "Saving..." : initialData ? "Save Changes" : "Create Matrix"}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
};