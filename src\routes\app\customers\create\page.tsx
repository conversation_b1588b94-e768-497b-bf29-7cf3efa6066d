"use client";

import { useNavigate } from "react-router";
import Cookies from "js-cookie";
import { createRetailStoreClient } from "@/lib/api/clients/service";
import { CustomerForm } from "@/routes/app/customers/CustomerForm";
import { type NewCustomerSchema } from "@/lib/api/customers/models";

export default function CreateCustomerPage() {
  const router = useNavigate();

  const handleSubmit = async (data: NewCustomerSchema) => {
    const token = Cookies.get('auth_token');
    const store_id = Number(Cookies.get("active_store"));

    if (!token || !store_id) {
      throw new Error('Authentication token or store ID is missing.');
    }

    await createRetailStoreClient(token, store_id, data);
    router('/app/customers');
  };

  return (
    <CustomerForm onSubmit={handleSubmit} />
  );
}
