// import React from 'react';
// import { Bar } from 'react-chartjs-2';
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   BarElement,
//   Title,
//   Tooltip,
//   Legend,
// } from 'chart.js';
// import type { LowStockItem } from '@/lib/api/reports/models';


// ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

// interface InventoryOverviewProps {
//     inventoryData: LowStockItem[] | null;
// }

// const InventoryOverview = ({ inventoryData }: InventoryOverviewProps) => {
//   if (!inventoryData) {
//     return <div>Loading... or Error</div>;
//   }

//   const labels = inventoryData.map(item => item.item_name);
//   const quantityData = inventoryData.map(item => item.quantity);

//   const barChartData = {
//     labels: labels,
//     datasets: [
//       {
//         label: 'Quantity',
//         data: quantityData,
//         backgroundColor: 'rgba(255, 99, 132, 0.5)',
//       },
//     ],
//   };

//   const barChartOptions = {
//     responsive: true,
//     maintainAspectRatio: false,
//     plugins: {
//       legend: {
//         position: 'top' as const,
//       },
//       title: {
//         display: true,
//         text: 'Inventory Levels',
//       },
//     },
//   };

//   return (
//     <div className="h-full flex flex-col">
//       <h2 className="text-lg font-semibold mb-4">Inventory Management</h2>
//       <div className="mb-6 flex-grow">
//         <h3 className="text-md font-medium mb-2">Low Stock Items</h3>
//         <div className="overflow-x-auto">
//           <table className="min-w-full divide-y divide-gray-300">
//             <thead className="bg-gray-50 dark:bg-gray-700">
//               <tr>
//                 <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
//                   ID
//                 </th>
//                 <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
//                   Name
//                 </th>
//                 <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900 dark:text-white">
//                   Quantity
//                 </th>
//               </tr>
//             </thead>
//             <tbody className="divide-y divide-gray-200 bg-white dark:bg-gray-800">
//               {inventoryData.map((item) => (
//                 <tr key={item.item_id}>
//                   <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{item.item_id}</td>
//                   <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{item.item_name}</td>
//                   <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500 dark:text-gray-300">{item.quantity}</td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>
//       </div>
//       <div className="flex-grow">
//         {barChartData && <Bar options={barChartOptions} data={barChartData} />}
//       </div>
//     </div>
//   );
// };

// export default InventoryOverview;

import { AlertTriangle, Package } from 'lucide-react';
import type { LowStockItem } from '@/lib/api/reports/models';

interface InventoryOverviewProps {
    inventoryData: LowStockItem[] | null;
}

const InventoryOverview = ({ inventoryData }: InventoryOverviewProps) => {
  if (!inventoryData) {
    return <div>Loading... or Error</div>;
  }

  // Sort by quantity (lowest first) for better visualization
  const sortedInventoryData = [...inventoryData].sort((a, b) => a.quantity - b.quantity);

  // Calculate summary statistics
  const criticalItems = inventoryData.filter(item => item.quantity <= 5).length;
  const warningItems = inventoryData.filter(item => item.quantity > 5 && item.quantity <= 10).length;
  const totalItems = inventoryData.length;

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-lg font-semibold mb-3 flex items-center gap-2">
          <Package className="h-5 w-5" />
          Inventory Management
        </h2>

        {/* Summary Cards */}
        <div className="grid grid-cols-3 gap-2 mb-4">
          <div className="text-center p-2 rounded-lg bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800">
            <div className="flex items-center justify-center gap-1 mb-1">
              <AlertTriangle className="h-3 w-3 text-red-600" />
              <div className="text-xs text-red-600 dark:text-red-400">Critical</div>
            </div>
            <div className="text-lg font-bold text-red-700 dark:text-red-300">
              {criticalItems}
            </div>
            <div className="text-xs text-red-600 dark:text-red-400">≤ 5 items</div>
          </div>

          <div className="text-center p-2 rounded-lg bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800">
            <div className="flex items-center justify-center gap-1 mb-1">
              <AlertTriangle className="h-3 w-3 text-yellow-600" />
              <div className="text-xs text-yellow-600 dark:text-yellow-400">Warning</div>
            </div>
            <div className="text-lg font-bold text-yellow-700 dark:text-yellow-300">
              {warningItems}
            </div>
            <div className="text-xs text-yellow-600 dark:text-yellow-400">6-10 items</div>
          </div>

          <div className="text-center p-2 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Package className="h-3 w-3 text-blue-600" />
              <div className="text-xs text-blue-600 dark:text-blue-400">Total Low Stock</div>
            </div>
            <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
              {totalItems}
            </div>
            <div className="text-xs text-blue-600 dark:text-blue-400">items</div>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div>
        <h3 className="text-md font-medium mb-3 flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-red-500" />
          Low Stock Items
        </h3>
        <div className="rounded-lg border max-h-[300px] overflow-y-auto">
          <table className="w-full">
            <thead className="sticky top-0 bg-background">
              <tr className="border-b bg-muted/50">
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground">
                  ID
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground">
                  Item Name
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground">
                  Quantity
                </th>
                <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground">
                  Status
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedInventoryData.map((item) => {
                const urgency = item.quantity <= 5 ? 'critical' : item.quantity <= 10 ? 'warning' : 'low';
                return (
                  <tr key={item.item_id} className="border-b last:border-b-0 hover:bg-muted/30">
                    <td className="px-3 py-2 text-xs text-muted-foreground">
                      {item.item_id}
                    </td>
                    <td className="px-3 py-2 text-xs font-medium">
                      {item.item_name}
                    </td>
                    <td className="px-3 py-2 text-xs">
                      <span className={`font-semibold ${
                        urgency === 'critical'
                          ? 'text-red-600 dark:text-red-400'
                          : urgency === 'warning'
                          ? 'text-yellow-600 dark:text-yellow-400'
                          : 'text-orange-600 dark:text-orange-400'
                      }`}>
                        {item.quantity}
                      </span>
                    </td>
                    <td className="px-3 py-2 text-xs">
                      <span className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                        urgency === 'critical'
                          ? 'bg-red-100 text-red-700 dark:bg-red-950/50 dark:text-red-300'
                          : urgency === 'warning'
                          ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-950/50 dark:text-yellow-300'
                          : 'bg-orange-100 text-orange-700 dark:bg-orange-950/50 dark:text-orange-300'
                      }`}>
                        {urgency === 'critical' ? 'Critical' : urgency === 'warning' ? 'Warning' : 'Low'}
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default InventoryOverview;