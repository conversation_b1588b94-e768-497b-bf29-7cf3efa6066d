import { useEffect, useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Axi<PERSON> } from 'recharts';
import { type ChartConfig, ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { fetchStoreMetrics } from '@/lib/api/reports/service';
import { getMe } from '@/lib/api/users/service';
import { getStores } from '@/lib/api/retailStores/service';
import type { Store } from '@/lib/api/retailStores/models';
import Cookies from 'js-cookie';
import { TrendingUp, Building2 } from 'lucide-react';

interface StorePerformanceData {
    id: number;
    name: string;
    sales: number;
    transactions: number;
    averageTransactionValue: number;
    currency: string;
}

const chartConfig = {
  sales: {
    label: "Sales",
    color: "hsl(210, 100%, 60%)", // Blue
  },
  transactions: {
    label: "Transactions",
    color: "hsl(142, 76%, 36%)", // Green
  },
} satisfies ChartConfig;



interface StorePerformanceProps {
    stores: Store[] | null;
}

const StorePerformance = ({ }: StorePerformanceProps) => {
    const [storeData, setStoreData] = useState<StorePerformanceData[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const authToken = Cookies.get('auth_token');

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true);
            setError(null);

            try {
                if (!authToken) {
                    throw new Error('Authentication token is missing');
                }

                const user = await getMe(authToken);
                const adminId = user.id;

                const metrics = await fetchStoreMetrics(authToken, adminId);
                const stores: Store[] = await getStores(authToken);

                const combinedData = metrics.map((metric) => {
                    const store = stores.find((store) => store.id === metric.store_id);
                    return {
                        id: metric.store_id,
                        name: store ? store.name : 'Unknown Store',
                        sales: metric.sales,
                        transactions: metric.transactions,
                        averageTransactionValue: metric.average_transaction_value,
                        currency: store?.currency || 'USD',
                    };
                });

                setStoreData(combinedData);
            } catch (err) {
                setError('Failed to load store performance data.');
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [authToken]);

    const formatNumber = (value: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
        }).format(value);
    };

    const formatMoney = (value: number, currency: string) => {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(value);
    };

    // Transform data for Recharts
    const salesChartData = storeData.map((store) => ({
        storeName: store.name,
        // Truncate long names for better display
        displayName: store.name.length > 12
            ? `${store.name.slice(0, 12)}...`
            : store.name,
        sales: store.sales,
    }));

    const transactionsChartData = storeData.map((store) => ({
        storeName: store.name,
        // Truncate long names for better display
        displayName: store.name.length > 12
            ? `${store.name.slice(0, 12)}...`
            : store.name,
        transactions: store.transactions,
    }));

    if (loading) {
        return (
            <div className="h-full flex justify-center items-center">
                <div className="animate-pulse flex flex-col items-center">
                    <div className="h-8 w-8 rounded-full border-3 border-t-blue-500 border-gray-200 animate-spin"></div>
                    <p className="mt-2 text-sm text-gray-500">Loading store data...</p>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="h-full flex justify-center items-center">
                <div className="text-center">
                    <div className="text-red-500 mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <p className="text-sm text-gray-600">{error}</p>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-6 h-full">
                <div className="flex flex-col space-y-6">
                    <div className="flex items-center justify-between">
                        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Store Performance</h2>
                    </div>

                    {/* Table with shadow and better spacing */}
                    <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead className="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Store Name</th>
                                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Sales</th>
                                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Transactions</th>
                                        <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Avg. Value</th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    {storeData.map((store) => (
                                        <tr key={store.id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                            <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">{store.name}</td>
                                            <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">
                                                {formatNumber(store.sales, store.currency)}
                                            </td>
                                            <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">{store.transactions}</td>
                                            <td className="px-4 py-3 text-sm text-right text-gray-900 dark:text-white">
                                                {formatMoney(store.averageTransactionValue, store.currency)}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    {/* Charts Grid */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1">
                        {/* Sales Chart */}
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[400px]">
                            <h3 className="text-md font-medium mb-3 text-gray-700 dark:text-gray-300 flex items-center gap-2">
                                <Building2 className="h-4 w-4" />
                                Sales
                            </h3>
                            <ChartContainer config={chartConfig} className="h-[350px] w-full">
                                <BarChart
                                    data={salesChartData}
                                    margin={{
                                        top: 20,
                                        right: 30,
                                        left: 20,
                                        bottom: 60,
                                    }}
                                >
                                    <XAxis
                                        dataKey="displayName"
                                        tickLine={false}
                                        axisLine={false}
                                        className="text-xs"
                                        angle={-45}
                                        textAnchor="end"
                                        height={80}
                                    />
                                    <YAxis
                                        tickLine={false}
                                        axisLine={false}
                                        className="text-xs"
                                        tickFormatter={(value) => new Intl.NumberFormat('en-US').format(value)}
                                    />
                                    <Bar
                                        dataKey="sales"
                                        fill="var(--color-sales)"
                                        radius={[4, 4, 0, 0]}
                                        className="cursor-pointer"
                                    />
                                    <ChartTooltip
                                        content={({ active, payload }) => {
                                            if (active && payload && payload.length) {
                                                const data = payload[0].payload;
                                                return (
                                                    <div className="rounded-lg border bg-background p-3 shadow-md">
                                                        <div className="grid gap-2">
                                                            <div className="font-medium">{data.storeName}</div>
                                                            <div className="grid gap-1">
                                                                <div className="flex items-center justify-between gap-2">
                                                                    <span className="text-sm text-muted-foreground">Sales:</span>
                                                                    <span className="text-sm font-medium">{new Intl.NumberFormat('en-US').format(data.sales)}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            return null;
                                        }}
                                    />
                                </BarChart>
                            </ChartContainer>
                        </div>

                        {/* Transactions Chart */}
                        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-4 min-h-[400px]">
                            <h3 className="text-md font-medium mb-3 text-gray-700 dark:text-gray-300 flex items-center gap-2">
                                <TrendingUp className="h-4 w-4" />
                                Transactions
                            </h3>
                            <ChartContainer config={chartConfig} className="h-[350px] w-full">
                                <BarChart
                                    data={transactionsChartData}
                                    margin={{
                                        top: 20,
                                        right: 30,
                                        left: 20,
                                        bottom: 60,
                                    }}
                                >
                                    <XAxis
                                        dataKey="displayName"
                                        tickLine={false}
                                        axisLine={false}
                                        className="text-xs"
                                        angle={-45}
                                        textAnchor="end"
                                        height={80}
                                    />
                                    <YAxis
                                        tickLine={false}
                                        axisLine={false}
                                        className="text-xs"
                                    />
                                    <Bar
                                        dataKey="transactions"
                                        fill="var(--color-transactions)"
                                        radius={[4, 4, 0, 0]}
                                        className="cursor-pointer"
                                    />
                                    <ChartTooltip
                                        content={({ active, payload }) => {
                                            if (active && payload && payload.length) {
                                                const data = payload[0].payload;
                                                return (
                                                    <div className="rounded-lg border bg-background p-3 shadow-md">
                                                        <div className="grid gap-2">
                                                            <div className="font-medium">{data.storeName}</div>
                                                            <div className="grid gap-1">
                                                                <div className="flex items-center justify-between gap-2">
                                                                    <span className="text-sm text-muted-foreground">Transactions:</span>
                                                                    <span className="text-sm font-medium">{data.transactions.toLocaleString()}</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                );
                                            }
                                            return null;
                                        }}
                                    />
                                </BarChart>
                            </ChartContainer>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default StorePerformance;