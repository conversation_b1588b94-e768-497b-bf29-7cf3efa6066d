"use client";

import React, { useState, useEffect } from 'react';
import StorePerformance from './StorePerformance';
import QuickStats from './QuickStats';

import { fetchDailyStoreAggregatedAverageSales } from '@/lib/api/reports/service';
import { getMe } from '@/lib/api/users/service';
import { getStores } from '@/lib/api/retailStores/service';
import { fetchDashboardStats } from '@/lib/api/reports/service';

import type { Store } from '@/lib/api/retailStores/models';
import Cookies from 'js-cookie';
import type { DailyStoreAggregatedAverageSales, DashboardStats } from '@/lib/api/reports/models';

function LoadingSpinner() {
    return (
        <div className="min-h-screen flex justify-center items-center bg-gray-50">
            <div className="animate-pulse flex flex-col items-center">
                <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
                <p className="mt-4 text-gray-600 font-medium">Loading dashboard...</p>
            </div>
        </div>
    );
}

export default function DashboardPage() {
    const [activeTab, setActiveTab] = React.useState('overview');
    const [storePerformanceData, setStorePerformanceData] = useState<Store[] | null>(null);
    const [dailyAverageSalesData, setDailyAverageSalesData] = useState<DailyStoreAggregatedAverageSales | null>(null);
    const [dashboardStatsData, setDashboardStatsData] = useState<DashboardStats | null>(null);

    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const token = Cookies.get('auth_token');
    const storeId = Cookies.get('active_store');

    useEffect(() => {
        async function fetchData() {
            if (!token || !storeId) {
                setError("Authentication token or store ID is missing.");
                setLoading(false);
                return;
            }

            try {
                const storeIdNum = Number(storeId);
                const [
                    storePerformance,
                    dailyAverageSales,
                    dashboardStats,
                ] = await Promise.all([
                    getStores(token),
                    fetchDailyStoreAggregatedAverageSales(token, storeIdNum),
                    getDashboardStats(token, storeIdNum)
                ]);

                setStorePerformanceData(storePerformance);
                setDailyAverageSalesData(dailyAverageSales);
                setDashboardStatsData(dashboardStats);
            } catch (err: any) {
                console.error("Error fetching data:", err);
                setError(err.message || "Failed to load reports. Please try again.");
            } finally {
                setLoading(false);
            }
        }

        fetchData();
    }, [token, storeId]);

    async function getDashboardStats(authToken: string, storeId: number) {
        const user = await getMe(authToken);
        return await fetchDashboardStats(authToken, user.id);
    }

    if (loading) {
        return <LoadingSpinner />;
    }

    if (error) {
        return <div className="p-4 text-red-500">{error}</div>;
    }

    return (
        <div className="p-4">
            {/* Top Bar */}
            <div className="flex items-center justify-between mb-4">
                <h1 className="text-xl font-semibold">Dashboard</h1>
            </div>

            {/* Content (Overview Tab) */}
            {activeTab === 'overview' && (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-12">
                    {/* Quick Stats (Top Row) */}
                    <QuickStats stats={dashboardStatsData} />

                    {/* Store Performance (Full Screen) */}
                    <div className="col-span-1 md:col-span-2 lg:col-span-12 rounded-lg border bg-card shadow-sm min-h-[calc(100vh-8rem)]">
                        <StorePerformance stores={storePerformanceData} />
                    </div>
                    {/* Recent Sales (Bottom Row, Full Width) */}
                    {/* <div className="col-span-1 md:col-span-2 lg:col-span-12 rounded-lg border bg-card p-4 shadow-sm">
    //                     <RecentSales />
    //                 </div> */}
                </div>
            )}
        </div>
    );
}