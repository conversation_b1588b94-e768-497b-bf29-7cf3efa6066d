export interface Attributes {
  id: number,
  url: string,
  name: string,
  inherited: boolean,
  store_id?: string,
  system_defined: boolean,
  date_created: string,
  date_updated?: string,
}

export interface AttributeValues {
  id: number,
  url: string,
  attribute_id: number,
  value: string,
  store_id?: string,
  date_created: string,
  date_updated?: string,
}

export interface ItemAttributesValues {
  id: number,
  url: string,
  item_id: number,
  attribute_value_id: number,
  position: number,
  store_id: number,
  date_created: string,
  date_updated?: string,
}

export interface MatrixAttributesValues {
  id: number,
  url: string,
  item_matrix_id: number,
  attribute_value_id: number,
  position: number,
  store_id: number,
  date_created: string,
  date_updated?: string,
}


export interface AttributePayload {
  name: string,
}


export interface AttributeValuesSchema {
  values: string[]
}
