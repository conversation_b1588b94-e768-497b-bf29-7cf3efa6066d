import { BASE_URL } from "@/routes/configs/constants";
import type { Sale, SellItemsSchema } from "./models";
import { type Receipt } from "../receipts/models";



export async function fetchSales(authToken: string, storeId: number): Promise<Sale[]> {
  const url = `${BASE_URL}/sales/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch sales: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching sales:", error);
    throw error;
  }
}


// Structure for the Pending response from backend (when status is 202)
interface SalePendingResponse {
  status: "pending"; // Should match backend JSON field
  message: string;
  receiptId: string; // Changed casing to match common JS/TS convention (camelCase)
  checkoutRequestId: string; // Changed casing
}

// Define a Discriminated Union for the result of sellItems
export type SellItemsResult =
  | { ok: true; status: 200; data: Receipt } // Completed (Cash) - Status 200
  | { ok: true; status: 202; data: SalePendingResponse } // Pending (M-Pesa) - Status 202
  | { ok: false; status: number; error: string }; // Any Error

// --- Modify sellItems function ---
export async function sellItems(authToken: string, payload: SellItemsSchema): Promise<SellItemsResult> {
  const url = `${BASE_URL}/sales/sell`;

  const cleanedItems = payload.items.map(({ has_vat, vat_percentage, ...rest }) => rest);

  const bodyPayload = {
    ...payload,
    items: cleanedItems
  };

  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": payload.items[0].store_id.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(bodyPayload),
    });

    // Clone the response before reading it
    const responseClone = response.clone();

    // Try parsing JSON first
    let responseBody: any;
    try {
      responseBody = await response.json();
    } catch (_jsonError) {
      // If JSON parsing fails, try reading as text from the cloned response
      const textError = await responseClone.text();
      console.error("Failed to parse JSON response. Response text:", textError);
      responseBody = { error: textError || `Request failed with status ${response.status}` };
    }

    // --- Check status codes and return appropriate result ---
    if (response.status === 200) {
      // OK - Synchronous success (e.g., Cash)
      return { ok: true, status: 200, data: responseBody as Receipt };
    } else if (response.status === 202) {
      // Accepted - Asynchronous process started (e.g., M-Pesa)
      return { ok: true, status: 202, data: responseBody as SalePendingResponse };
    } else {
      // Other non-successful status codes
      const errorMessage = responseBody?.error // Prefer specific error from JSON
        || responseBody?.message // Or message field
        || `Request failed: ${response.statusText} (${response.status})`; // Fallback
      console.error("Sell items API error:", errorMessage, responseBody);
      return { ok: false, status: response.status, error: errorMessage };
    }

  } catch (error: unknown) {
    // Network errors or errors during fetch/JSON parsing
    return {
      ok: false,
      status: 500,
      error: error instanceof Error ? error.message : "An unexpected network error occurred"
    };
  }
}
