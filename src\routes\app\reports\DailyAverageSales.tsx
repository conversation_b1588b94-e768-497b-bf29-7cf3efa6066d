import { Line, LineChart } from 'recharts';
import { type ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Link } from "react-router";
import Cookies from 'js-cookie';
import { formatCurrency } from '@/lib/utils/currency';

const labels: string[] = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

interface DailyAverageSalesProps {
  dailyAvgSalesData: any[] | null;
}

const chartConfig = {
  dailySales: {
    label: "Daily Sales",
    color: "hsl(210, 100%, 60%)", // Similar to rgb(53, 162, 235)
  },
  average: {
    label: "Average",
    color: "hsl(348, 100%, 70%)", // Similar to rgb(255, 99, 132)
  },
} satisfies ChartConfig;

const DailyAverageSales = ({ dailyAvgSalesData }: DailyAverageSalesProps) => {
  if (!dailyAvgSalesData) {
    return <div>Loading... or Error</div>;
  }

  const salesMap: Record<string, number> = dailyAvgSalesData.reduce((acc, { day_of_week, average_sale }) => {
    acc[day_of_week] = average_sale;
    return acc;
  }, {} as Record<string, number>);

  const salesData: number[] = labels.map(day => salesMap[day] ?? 0);

  const totalSales = salesData.reduce((sum, val) => sum + val, 0);
  const daysWithData = salesData.filter(val => val > 0).length;
  const averageSales = daysWithData > 0 ? totalSales / daysWithData : 0;



  const todaySales = salesData[new Date().getDay()] || 0;
  const percentChange = averageSales ? ((todaySales - averageSales) / averageSales) * 100 : 0;

  // Transform data for Recharts
  const chartData = labels.map((day, index) => ({
    day: day.slice(0, 3), // Shortened day names for better display
    dailySales: salesData[index],
    average: averageSales,
  }));

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Daily Average Sales</h3>
      <div className="text-3xl font-bold text-gray-900 dark:text-white">
        {formatCurrency(averageSales)}
      </div>
      <div className={`text-sm ${percentChange >= 0 ? 'text-green-500' : 'text-red-500'}`}>
        {percentChange >= 0 ? '+' : ''}{percentChange.toFixed(2)}% {percentChange >= 0 ? 'above' : 'below'} Average
      </div>
      <div className="h-32">
        <ChartContainer config={chartConfig} className="h-full w-full">
          <LineChart
            data={chartData}
            margin={{
              top: 5,
              right: 5,
              left: 5,
              bottom: 5,
            }}
          >
            <Line
              type="monotone"
              dataKey="dailySales"
              stroke="var(--color-dailySales)"
              strokeWidth={2}
              dot={false}
            />
            <Line
              type="monotone"
              dataKey="average"
              stroke="var(--color-average)"
              strokeWidth={2}
              strokeDasharray="5 5"
              dot={false}
            />
            <ChartTooltip
              content={<ChartTooltipContent />}
              cursor={false}
            />
          </LineChart>
        </ChartContainer>
      </div>
      <Link to="/reports/sales" className="text-blue-500 hover:underline text-sm">
        View Detailed Report
      </Link>
    </div>
  );
};

export default DailyAverageSales;