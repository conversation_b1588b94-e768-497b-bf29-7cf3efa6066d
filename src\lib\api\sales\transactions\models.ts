export interface PaymentTransaction {
    id: number;
    url: string;
    payment_method: string;
    merchant_request_id: string | null;
    checkout_request_id: string | null;
    result_code: number | null;
    result_desc: string | null;
    amount: string;
    mpesa_receipt_number: string | null;
    transaction_date: string;
    phone_number: string | null;
    card_reference: string | null;
    card_type: string | null;
    card_last_four: string | null;
    status: string;
    created_at: string | null;
    receipt_id: number;
}