export interface Exchange {
  id: number;
  url: string;
  original_sale_id: number;
  original_item_id: number;
  transaction_id?: number;
  store_id: number;
  quantity_exchanged: number;
  exchange_reason?: string;
  exchanged_with_item_id: number;
  exchange_value?: number;
  timestamp: string;
  receipt_number: string;
  comments?: string;
  staff_id?: string;
}

export interface ExchangeSchema {
  original_sale_id: number;
  original_item_id: number;
  quantity_exchanged: number;
  exchange_reason?: string;
  exchanged_with_item_id?: string;
  exchange_value?: number;
  receipt_number: string;
  comments?: string;
}
