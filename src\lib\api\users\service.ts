import type { NewStaff, User, UserInfoResponse } from "./models";

import { BASE_URL } from "@/routes/configs/constants";
import Cookies from "js-cookie";

export async function list_users(
  authToken: string,
  storeId: number,
): Promise<UserInfoResponse[]> {
  try {
    const response = await fetch(`${BASE_URL}/users/admin/${storeId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function create_user(authToken: string, storeId: number, user: NewStaff): Promise<User> {
  try {
    const response = await fetch(`${BASE_URL}/users/admin/${storeId}/new`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(user),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function revoke_user_access(
  authToken: string,
  storeId: number,
  userId: number,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/deactivate`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function reactivate_user_access(
  authToken: string,
  storeId: number,
  userId: number,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/reactivate`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getUser(authToken: string, storeId: number, userId: number): Promise<UserInfoResponse> {

  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }

}

export async function updateUser(authToken: string, storeId: number, userId: number, user: NewStaff): Promise<User> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
        body: JSON.stringify(user),
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateUserRole(authToken: string, storeId: number, userId: number, role_id?: number): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/role/${role_id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();

  } catch (error) {
    throw error;
  }
}

export async function getMe(authToken: string): Promise<User> {
  try {
    const storeId = Number(Cookies.get("active_store"));
    const response = await fetch(`${BASE_URL}/users/me`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function delete_user(
  authToken: string,
  storeId: number,
  userId: number,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/users/admin/${storeId}/${userId}/delete`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch users");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}
