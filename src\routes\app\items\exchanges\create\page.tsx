"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { ExchangeForm } from "@/routes/app/items/exchanges/ExchangeForm";
import type { Item } from "@/lib/api/items/models";
import { fetchItems } from "@/lib/api/items/service";
import { createExchange } from "@/lib/api/items/exchanges/service";
import type { ExchangeSchema } from "@/lib/api/items/exchanges/models";
import type { Receipt } from "@/lib/api/receipts/models";
import { fetchReceipts } from "@/lib/api/receipts/service";
import type { Sale } from "@/lib/api/sales/models";
import { fetchSales } from "@/lib/api/sales/service";


export default function CreateExchangePage() {
  const [items, setItems] = useState<Item[]>([]);
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [loading, setLoading] = useState(true);

  const token = Cookies.get("auth_token");
  const store_id = Number(Cookies.get("active_store"));

  useEffect(() => {
    const fetchData = async () => {
      if (token && store_id) {
        try {
          const [fetchedItems, fetchedReceipts, fetchedSales] = await Promise.all([
            fetchItems(token, store_id),
            fetchReceipts(token, store_id),
            fetchSales(token, store_id)
          ]);
          setItems(fetchedItems);
          setReceipts(fetchedReceipts);
          setSales(fetchedSales);
        } catch (error) {
          console.error("Failed to fetch data:", error);
        } finally {
          setLoading(false);
        }
      }
    };
    fetchData();
  }, [token, store_id]);


  const handleSubmit = async (data: ExchangeSchema) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    await createExchange(token, store_id, data);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading exchange details...</p>
        </div>
      </div>
    );
  }

  return (
    <ExchangeForm
      onSubmit={handleSubmit}
      items={items}
      receipts={receipts}
      sales={sales}
    />
  );
}
