import { BASE_URL } from "@/routes/configs/constants";
import type { Customer, NewCustomerSchema } from "./models";

export async function fetchCustomers(
  authToken: string,
  storeId: number,
): Promise<Customer[]> {
  const url = `${BASE_URL}/customers/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch customers: ${errorMessage.error}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching customers:", error);
    throw error;
  }
}

export async function createCustomer(
  authToken: string,
  storeId: number,
  customer: NewCustomerSchema,
): Promise<Customer> {
  const url = `${BASE_URL}/customers/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(customer),
    });

    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create customer: ${errorMessage.error}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating customer:", error);
    throw error;
  }
}

export async function deleteCustomer(
  authToken: string,
  storeId: number,
  customer_id: number,
): Promise<void> {
  const url = `${BASE_URL}/customers/${storeId}/${customer_id}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (response.status != 204) {
      const errorMessage = await response.json();
      throw new Error(`Failed to delete customer: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error deleting customer:", error);
    throw error;
  }
}
