"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alog<PERSON>lose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { CategorySchema } from "@/lib/api/categories/models";
import { Textarea } from "@/components/ui/textarea";
import Cookies from "js-cookie";
import { CreateCategory } from "@/lib/api/categories/service";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";

const categoryFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
});

type CategoryFormData = z.infer<typeof categoryFormSchema>;

export interface CategoryModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: CategorySchema) => Promise<void>;
}

const CategoryModal: React.FC<CategoryModalProps> = ({
  open,
  onOpenChange,
  onSubmit,
}) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);

  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categoryFormSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const token = Cookies.get("token");
  const store_id = Number(Cookies.get("store_id"));

  const handleSubmit = async (data: CategoryFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      if (!token || !store_id) {
        throw new Error("Authentication token or store ID is missing.");
      }

      const payload: CategorySchema = {
        name: data.name,
        description: data.description || "",
      };

      await CreateCategory(token, store_id, payload);
      setSuccess(true);
      form.reset();
      onOpenChange(false);
      if (onSubmit) {
        await onSubmit(payload);
      }
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Category</DialogTitle>
          <DialogClose />
        </DialogHeader>

        {error && <AlertError message={error} />}
        {success && <AlertSuccess message="Category added successfully" />}

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              {...form.register("name")}
              placeholder="Category name"
              className={form.formState.errors.name ? "border-red-500" : ""}
            />
            {form.formState.errors.name && (
              <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Category description"
            />
          </div>

          <DialogFooter className="sm:justify-start">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Category"}
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CategoryModal;
