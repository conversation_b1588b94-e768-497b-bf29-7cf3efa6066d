import type { PaymentTransaction } from "./models";
import { BASE_URL } from "@/routes/configs/constants";

export async function fetchMpesaTransactions(authToken: string, storeId: number): Promise<{ success: boolean; data: PaymentTransaction[] }> {
  const url = `${BASE_URL}/transactions/mpesa/data/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      let errorMessage = `Failed to fetch M-Pesa transactions: ${response.status}`;
      try {
        const errorBody = await response.json();
        if (errorBody && errorBody.error) {
          errorMessage += ` - ${errorBody.error}`;
        }
      } catch (_jsonError) {
        // If parsing JSON error fails, just use the status text
        errorMessage += ` - ${response.statusText}`;
      }
      throw new Error(errorMessage);
    }

    const responseData = await response.json();
    return responseData as { success: boolean; data: PaymentTransaction[] };

  } catch (error) {
    console.error("Error fetching M-Pesa transactions:", error);
    throw error;
  }
}