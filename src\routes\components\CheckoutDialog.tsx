import React, { useMemo, useState, useEffect, useCallback } from "react";
// import Image from 'next/image';
import { toast } from "sonner";
import {
    Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Card } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
    Trash2, Loader2, ShoppingCart, Plus, Minus, ArrowLeft, ArrowRight, Check, Smartphone, Banknote, Receipt
} from "lucide-react";
import { type PaymentMethod } from "@/lib/api/sales/models";

// Assuming this is the formatCurrency function you want to use
import { formatCurrency } from "@/lib/utils/currency";

export interface CartItem {
    id: number;
    name: string;
    price: number;
    quantity: number;
    image?: string;
    has_discount: boolean;
    discount: number;
    vat_included: boolean;
    vat_percentage: number;
    brand?: string;
    category?: string;
}

interface CheckoutDialogProps {
    isOpen: boolean;
    onOpenChange: (open: boolean) => void;
    cart: CartItem[];
    onUpdateQuantity: (itemId: number, quantity: number) => void;
    onUpdatePrice: (itemId: number, price: number) => void;
    onRemoveItem: (itemId: number) => void;
    onCheckout: () => Promise<void>;
    isLoading: boolean;
    // storeCurrency?: string; // Removed as it's not passed to formatCurrency directly
    paymentMethod: PaymentMethod | null;
    mpesaPhoneNumber: string;
    onPaymentMethodChange: (method: PaymentMethod | null) => void;
    onMpesaPhoneNumberChange: (phone: string) => void;
    itemDiscounts?: { [itemId: string]: boolean };
    onToggleItemDiscount?: (itemId: string) => void;
    itemVatSettings?: { [itemId: string]: { has_vat: boolean; vat_percentage: number } };
    onToggleItemVat?: (itemId: string) => void;
    onUpdateItemVatPercentage?: (itemId: string, percentage: number) => void;
    cartTotal: number;
    saleStatusMessage?: string | null;
}

// Types and Constants
type CheckoutStep = 'cart' | 'summary' | 'payment';
const STEPS: { id: CheckoutStep; name: string }[] = [
    { id: 'cart', name: 'Review Cart' },
    { id: 'summary', name: 'Order Summary' },
    { id: 'payment', name: 'Payment' },
];


// This function is now more defensive. It ensures that if item data
// like price or quantity is missing or invalid, it defaults to 0 for calculations,
// preventing NaN errors and resolving the "0.00" display issue.
const calculateItemTotal = (
    item: CartItem,
    hasDiscount: boolean,
    hasVat: boolean,
    vatPercentage: number
) => {
    const basePrice = Number(item.price) || 0;
    const quantity = Number(item.quantity) || 0;
    const discountPerc = Number(item.discount) || 0;
    const vatPerc = Number(vatPercentage) || 0;

    const baseTotal = basePrice * quantity;
    const discountAmount = hasDiscount ? baseTotal * (discountPerc / 100) : 0;
    const afterDiscount = baseTotal - discountAmount;
    const vatAmount = hasVat ? afterDiscount * (vatPerc / 100) : 0;
    const finalTotal = afterDiscount + vatAmount;

    return { baseTotal, discountAmount, afterDiscount, vatAmount, finalTotal };
};

// Sub-component Props Types
type CartStepProps = Pick<CheckoutDialogProps,
    'cart' | 'onUpdateQuantity' | 'onUpdatePrice' | 'onRemoveItem' | 'itemDiscounts' |
    'onToggleItemDiscount' | 'itemVatSettings' | 'onToggleItemVat' | 'onUpdateItemVatPercentage' |
    'isLoading' | 'cartTotal'
>; // Removed storeCurrency

type SummaryStepProps = Pick<CheckoutDialogProps,
    'cart' | 'itemDiscounts' | 'itemVatSettings' | 'cartTotal'
>; // Removed storeCurrency

type PaymentStepProps = Pick<CheckoutDialogProps,
    'paymentMethod' | 'onPaymentMethodChange' | 'mpesaPhoneNumber' | 'onMpesaPhoneNumberChange' |
    'isLoading' | 'cartTotal'
>; // Removed storeCurrency

// Cart Step Component
const CartStep: React.FC<CartStepProps> = ({
    cart, onUpdateQuantity, onUpdatePrice, onRemoveItem, itemDiscounts = {}, onToggleItemDiscount,
    itemVatSettings = {}, onToggleItemVat, onUpdateItemVatPercentage, isLoading, cartTotal
}) => ( // Removed storeCurrency = "UGX" from destructuring
    <div className="p-4 sm:p-6 space-y-6">
        {cart.map((item) => {
            const hasDiscount = Boolean(Boolean(itemDiscounts[item.id]) && item.has_discount && item.discount);
            const hasVat = Boolean(itemVatSettings[item.id]?.has_vat) || item.vat_included;
            const vatPercentage = itemVatSettings[item.id]?.vat_percentage || item.vat_percentage;

            const totals = calculateItemTotal(item, hasDiscount, hasVat, vatPercentage);
            const itemDisplayTotal = cart.length === 1 ? cartTotal : totals.finalTotal;

            // FIX: Calculate the discount amount based on the reliable cartTotal for single-item carts.
            let calculatedDiscountAmount = 0;
            let originalPrice = totals.baseTotal;
            if (hasDiscount && item.discount && item.discount > 0) {
                if (cart.length === 1) {
                    const discountFraction = item.discount / 100;
                    const priceBeforeVat = hasVat ? cartTotal / (1 + (vatPercentage / 100)) : cartTotal;
                    originalPrice = priceBeforeVat / (1 - discountFraction);
                    calculatedDiscountAmount = originalPrice - priceBeforeVat;
                } else {
                    calculatedDiscountAmount = totals.discountAmount;
                }
            }

            return (
                <Card key={item.id} className="p-4 sm:p-6 shadow-sm border-0 bg-gradient-to-r from-white to-gray-50/50">
                    <div className="flex flex-col md:flex-row gap-6">
                        <div className="relative w-full md:w-24 h-32 md:h-24 rounded-xl overflow-hidden bg-gradient-to-br from-gray-100 to-gray-200 flex-shrink-0 shadow-inner">
                            <img
                                src={item.image || '/placeholder-image.png'}
                                alt={item.name}
                                className="object-cover absolute inset-0 w-full h-full"
                            />

                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="flex justify-between items-start mb-3">
                                <div>
                                    <h3 className="font-bold text-lg leading-tight text-gray-900 mb-1">
                                        {item.name}
                                    </h3>
                                    <div className="flex flex-wrap gap-2">
                                        <Badge variant="secondary" className="text-xs">{item.brand}</Badge>
                                        <Badge variant="outline" className="text-xs">{item.category}</Badge>
                                    </div>
                                </div>
                                <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => onRemoveItem(item.id)}
                                    className="text-gray-400 hover:text-red-500 hover:bg-red-50 h-9 w-9 rounded-full transition-all duration-200 flex-shrink-0"
                                    disabled={isLoading}
                                >
                                    <Trash2 className="h-4 w-4" />
                                </Button>
                            </div>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
                                <div className="flex items-center justify-between bg-white rounded-lg border p-3">
                                    <span className="text-sm font-medium text-gray-600">Quantity</span>
                                    <div className="flex items-center space-x-2 sm:space-x-3">
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => onUpdateQuantity(item.id, Math.max(1, item.quantity - 1))}
                                            className="h-8 w-8 rounded-full border-2"
                                            disabled={isLoading || item.quantity <= 1}
                                        >
                                            <Minus className="h-3 w-3" />
                                        </Button>
                                        <span className="w-10 text-center font-bold text-lg">{item.quantity}</span>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => onUpdateQuantity(item.id, item.quantity + 1)}
                                            className="h-8 w-8 rounded-full border-2"
                                            disabled={isLoading}
                                        >
                                            <Plus className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>

                                <div className="flex items-center justify-between bg-white rounded-lg border p-3">
                                    <span className="text-sm font-medium text-gray-600">Unit Price</span>
                                    <div className="flex items-center space-x-2">
                                        {/* Removed explicit storeCurrency display as formatCurrency handles it */}
                                        <Input
                                            type="number"
                                            value={item.price}
                                            onChange={(e) => onUpdatePrice(item.id, parseFloat(e.target.value) || 0)}
                                            className="w-24 h-8 text-right border-0 bg-gray-50 font-medium"
                                            disabled={isLoading}
                                        />
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-4">
                                {item.has_discount && item.discount && (
                                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                                        <div className="flex items-center space-x-3">
                                            <Checkbox
                                                id={`discount-${item.id}`}
                                                checked={Boolean(itemDiscounts[item.id])}
                                                onCheckedChange={() => onToggleItemDiscount?.(item.id.toString())}
                                                disabled={isLoading}
                                                className="border-green-300"
                                            />
                                            <Label
                                                htmlFor={`discount-${item.id}`}
                                                className="text-sm font-medium text-green-700 cursor-pointer"
                                            >
                                                Apply {item.discount}% Discount
                                            </Label>
                                        </div>
                                        {Boolean(itemDiscounts[item.id]) && calculatedDiscountAmount > 0 && (
                                            <span className="text-sm font-bold text-green-600">
                                                - {formatCurrency(calculatedDiscountAmount)} (deducted)
                                            </span>
                                        )}
                                    </div>
                                )}

                                {(item.vat_included || item.vat_percentage > 0) && (
                                    <div className="flex flex-col sm:flex-row sm:items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200">
                                        <div className="flex items-center space-x-3">
                                            <Checkbox
                                                id={`vat-${item.id}`}
                                                checked={hasVat}
                                                disabled={true}
                                                className="border-orange-300"
                                            />
                                            <Label
                                                htmlFor={`vat-${item.id}`}
                                                className="text-sm font-medium text-orange-700"
                                            >
                                                VAT Included in Price
                                            </Label>
                                        </div>

                                        {hasVat && (
                                            <div className="flex items-center justify-between flex-1">
                                                <div className="flex items-center space-x-2">
                                                    <span className="text-sm text-orange-600 font-medium">VAT %:</span>
                                                    <Input
                                                        type="number"
                                                        value={vatPercentage}
                                                        className="w-20 h-8 text-center border-0 bg-gray-100 font-medium"
                                                        disabled={true}
                                                    />
                                                </div>
                                                <span className="text-sm font-bold text-orange-600">
                                                    {formatCurrency(totals.vatAmount)} (included)
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            <div className="mt-4 pt-4 border-t border-gray-200">
                                <div className="flex justify-between items-center">
                                    <span className="text-lg font-medium text-gray-600">Item Total:</span>
                                    <div className="text-right">
                                        {hasDiscount && calculatedDiscountAmount > 0 && (
                                            <div className="text-sm text-gray-500 line-through">
                                                {formatCurrency(originalPrice)}
                                            </div>
                                        )}
                                        <div className="text-xl font-bold text-gray-900">
                                            {formatCurrency(itemDisplayTotal)}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </Card>
            );
        })}
    </div>
);

// Summary Step Component
const SummaryStep: React.FC<SummaryStepProps> = ({
    cart, itemDiscounts = {}, itemVatSettings = {}, cartTotal
}) => { // Removed storeCurrency = "UGX" from destructuring
    const calculations = useMemo(() => {
        // If there's only one item, derive the breakdown from the reliable cartTotal.
        if (cart.length === 1) {
            const item = cart[0];
            const hasDiscount = Boolean(itemDiscounts[item.id]) && item.has_discount && item.discount;
            const hasVat = Boolean(itemVatSettings[item.id]?.has_vat) || item.vat_included;
            const vatPercentage = itemVatSettings[item.id]?.vat_percentage || item.vat_percentage;

            let subtotal = cartTotal;
            let totalDiscount = 0;
            let totalVat = 0;

            const priceBeforeVat = hasVat ? cartTotal / (1 + (vatPercentage / 100)) : cartTotal;
            if (hasVat) {
                totalVat = cartTotal - priceBeforeVat;
            }

            if (hasDiscount && item.discount) {
                const discountFraction = item.discount / 100;
                const originalPrice = priceBeforeVat / (1 - discountFraction);
                totalDiscount = originalPrice - priceBeforeVat;
                subtotal = originalPrice;
            } else {
                subtotal = priceBeforeVat;
            }

            return { subtotal, totalDiscount, totalVat };
        }

        // Fallback for multi-item carts (will show 0s if item prices are 0).
        let subtotal = 0;
        let totalDiscount = 0;
        let totalVat = 0;

        cart.forEach(item => {
            const hasDiscount = Boolean(Boolean(itemDiscounts[item.id]) && item.has_discount && item.discount);
            const hasVat = Boolean(itemVatSettings[item.id]?.has_vat) || item.vat_included;
            const vatPercentage = itemVatSettings[item.id]?.vat_percentage || item.vat_percentage;

            const totals = calculateItemTotal(item, hasDiscount, hasVat, vatPercentage);

            subtotal += totals.baseTotal;
            totalDiscount += totals.discountAmount;
            totalVat += totals.vatAmount;
        });

        return { subtotal, totalDiscount, totalVat };
    }, [cart, itemDiscounts, itemVatSettings, cartTotal]);

    return (
        <div className="p-4 sm:p-6">
            <div className="max-w-2xl mx-auto space-y-6">
                <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
                    <h3 className="text-2xl font-bold mb-6 text-gray-900 flex items-center">
                        <Receipt className="mr-3 h-6 w-6 text-blue-500" />
                        Order Summary
                    </h3>

                    <div className="space-y-4">
                        {cart.map(item => {
                            const hasDiscount = Boolean(Boolean(itemDiscounts[item.id]) && item.has_discount && item.discount);
                            const hasVat = Boolean(itemVatSettings[item.id]?.has_vat) || item.vat_included;
                            const vatPercentage = itemVatSettings[item.id]?.vat_percentage || item.vat_percentage;
                            const totals = calculateItemTotal(item, hasDiscount, hasVat, vatPercentage);

                            const itemDisplayTotal = cart.length === 1 ? cartTotal : totals.finalTotal;
                            let originalPrice = totals.baseTotal;

                            if (cart.length === 1 && hasDiscount && item.discount) {
                                const discountFraction = item.discount / 100;
                                const priceBeforeVat = hasVat ? cartTotal / (1 + (vatPercentage / 100)) : cartTotal;
                                originalPrice = priceBeforeVat / (1 - discountFraction);
                            } else if (cart.length === 1) {
                                originalPrice = cartTotal; // No discount, original price is final price
                            }

                            return (
                                <div key={item.id} className="flex justify-between items-center py-3 border-b border-gray-100 last:border-0">
                                    <div className="flex-1 min-w-0 pr-4">
                                        <div className="font-medium text-gray-900 truncate">{item.name}</div>
                                        <div className="text-sm text-gray-500">
                                            {item.quantity} × {formatCurrency(originalPrice / item.quantity)}
                                            {hasDiscount && <span className="text-green-600 ml-2">(-{item.discount}%)</span>}
                                            {hasVat && <span className="text-orange-600 ml-2">(+{vatPercentage}% VAT)</span>}
                                        </div>
                                    </div>
                                    <div className="text-right flex-shrink-0">
                                        {itemDisplayTotal !== originalPrice && originalPrice > 0 && (
                                            <div className="text-sm text-gray-400 line-through">
                                                {formatCurrency(originalPrice)}
                                            </div>
                                        )}
                                        <div className="font-bold text-gray-900">
                                            {formatCurrency(itemDisplayTotal)}
                                        </div>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </Card>

                <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                    <h4 className="text-lg font-bold mb-4 text-gray-900">Payment Breakdown</h4>
                    <div className="space-y-3">
                        <div className="flex justify-between text-gray-600">
                            <span>Subtotal ({cart.length} items)</span>
                            <span className="font-medium">{formatCurrency(calculations.subtotal)}</span>
                        </div>

                        {calculations.totalDiscount > 0 && (
                            <div className="flex justify-between text-green-600">
                                <span>Total Discounts</span>
                                <span className="font-medium">-{formatCurrency(calculations.totalDiscount)}</span>
                            </div>
                        )}

                        {calculations.totalVat > 0 && (
                            <div className="flex justify-between text-orange-600">
                                <span>Total VAT</span>
                                <span className="font-medium">+{formatCurrency(calculations.totalVat)}</span>
                            </div>
                        )}

                        <Separator className="my-4" />

                        <div className="flex justify-between text-2xl font-bold text-gray-900 bg-white p-4 rounded-lg shadow-sm">
                            <span>Grand Total</span>
                            <span className="text-blue-600">{formatCurrency(cartTotal)}</span>
                        </div>
                    </div>
                </Card>
            </div>
        </div>
    );
};

// Payment Step Component
const PaymentStep: React.FC<PaymentStepProps> = ({
    paymentMethod, onPaymentMethodChange, mpesaPhoneNumber, onMpesaPhoneNumberChange,
    isLoading, cartTotal
}) => ( // Removed storeCurrency = "UGX" from destructuring
    <div className="p-4 sm:p-6">
        <div className="max-w-2xl mx-auto space-y-6">
            <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-white to-gray-50">
                <h3 className="text-xl font-bold mb-6 text-gray-900">Choose Payment Method</h3>
                <div className="grid gap-4">
                    <Card
                        className={`p-6 cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${paymentMethod === 'mpesa'
                                ? 'ring-2 ring-green-500 bg-green-50 shadow-lg'
                                : 'hover:bg-gray-50 hover:shadow-md'
                            }`}
                        onClick={() => onPaymentMethodChange('mpesa')}
                    >
                        <div className="flex items-center gap-4">
                            <div className="bg-green-100 p-3 rounded-xl">
                                <Smartphone className="h-8 w-8 text-green-600" />
                            </div>
                            <div className="flex-1">
                                <h4 className="font-bold text-lg">M-Pesa</h4>
                                <p className="text-gray-600">Pay securely via M-Pesa STK Push</p>
                            </div>
                            {paymentMethod === 'mpesa' && (
                                <div className="bg-green-500 p-1 rounded-full">
                                    <Check className="h-4 w-4 text-white" />
                                </div>
                            )}
                        </div>
                    </Card>

                    {paymentMethod === 'mpesa' && (
                        <div className="ml-4 p-4 bg-green-50 rounded-lg border border-green-200">
                            <Label htmlFor="mpesa-phone" className="text-sm font-medium text-green-800 mb-2 block">
                                M-Pesa Phone Number
                            </Label>
                            <Input
                                id="mpesa-phone"
                                type="tel"
                                placeholder="07XXXXXXXX or 254XXXXXXXX"
                                value={mpesaPhoneNumber}
                                onChange={(e) => onMpesaPhoneNumberChange(e.target.value)}
                                disabled={isLoading}
                                className="border-green-300 focus:ring-green-500"
                            />
                        </div>
                    )}

                    <Card
                        className={`p-6 cursor-pointer transition-all duration-300 transform hover:scale-[1.02] ${paymentMethod === 'cash'
                                ? 'ring-2 ring-blue-500 bg-blue-50 shadow-lg'
                                : 'hover:bg-gray-50 hover:shadow-md'
                            }`}
                        onClick={() => onPaymentMethodChange('cash')}
                    >
                        <div className="flex items-center gap-4">
                            <div className="bg-blue-100 p-3 rounded-xl">
                                <Banknote className="h-8 w-8 text-blue-600" />
                            </div>
                            <div className="flex-1">
                                <h4 className="font-bold text-lg">Cash Payment</h4>
                                <p className="text-gray-600">Pay with physical cash</p>
                            </div>
                            {paymentMethod === 'cash' && (
                                <div className="bg-blue-500 p-1 rounded-full">
                                    <Check className="h-4 w-4 text-white" />
                                </div>
                            )}
                        </div>
                    </Card>
                </div>
            </Card>

            <Card className="p-6 shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-gray-900">Total Amount</span>
                    <span className="text-2xl font-bold text-blue-600">
                        {formatCurrency(cartTotal)}
                    </span>
                </div>
            </Card>
        </div>
    </div>
);

// Main CheckoutDialog Component
export const CheckoutDialog: React.FC<CheckoutDialogProps> = ({
    isOpen, onOpenChange, cart, onUpdateQuantity, onUpdatePrice, onRemoveItem, onCheckout, isLoading,
    // storeCurrency = "KES", // Removed this default here
    paymentMethod, mpesaPhoneNumber, onPaymentMethodChange, onMpesaPhoneNumberChange,
    itemDiscounts = {}, onToggleItemDiscount, itemVatSettings = {}, onToggleItemVat, onUpdateItemVatPercentage,
    cartTotal, saleStatusMessage
}) => {
    const [currentStep, setCurrentStep] = useState<CheckoutStep>('cart');
    const [isProcessing, setIsProcessing] = useState(false);

    const currentStepIndex = STEPS.findIndex(step => step.id === currentStep);

    const canProceed = useMemo(() => {
        if (currentStep === 'cart') return cart.length > 0;
        if (currentStep === 'payment') {
            if (!paymentMethod) return false;
            if (paymentMethod === 'mpesa') {
                const mpesaRegex = /^(?:0|(?:254|\+254))(7|1)[0-9]{8}$/;
                return mpesaPhoneNumber && mpesaRegex.test(mpesaPhoneNumber);
            }
            return true;
        }
        return true;
    }, [currentStep, cart.length, paymentMethod, mpesaPhoneNumber]);

    const handleNext = useCallback(() => {
        const nextIndex = currentStepIndex + 1;
        if (nextIndex < STEPS.length) {
            setCurrentStep(STEPS[nextIndex].id);
        }
    }, [currentStepIndex]);

    const handlePrevious = useCallback(() => {
        const prevIndex = currentStepIndex - 1;
        if (prevIndex >= 0) {
            setCurrentStep(STEPS[prevIndex].id);
        }
    }, [currentStepIndex]);

    const handleCheckout = useCallback(async () => {
        if (!canProceed) {
            if (!paymentMethod) {
                toast.error("Please select a payment method.");
                return;
            }
            if (paymentMethod === "mpesa") {
                const mpesaRegex = /^(?:0|(?:254|\+254))(7|1)[0-9]{8}$/;
                if (!mpesaPhoneNumber || !mpesaRegex.test(mpesaPhoneNumber)) {
                    toast.error("Please enter a valid M-Pesa number.");
                    return;
                }
            }
            return;
        }

        setIsProcessing(true);
        try {
            await onCheckout();
        } catch (error) {
            console.error('Checkout failed:', error);
        } finally {
            setIsProcessing(false);
        }
    }, [canProceed, onCheckout, paymentMethod, mpesaPhoneNumber]);

    useEffect(() => {
        if (!isOpen) {
            setTimeout(() => setCurrentStep('cart'), 300); // Reset step after dialog closes
            setIsProcessing(false);
        }
    }, [isOpen]);

    useEffect(() => {
        if (saleStatusMessage) {
            toast.error("Checkout Failed", { description: saleStatusMessage });
        }
    }, [saleStatusMessage]);

    const renderStepContent = () => {
        switch (currentStep) {
            case 'cart':
                return <CartStep
                    cart={cart}
                    onUpdateQuantity={onUpdateQuantity}
                    onUpdatePrice={onUpdatePrice}
                    onRemoveItem={onRemoveItem}
                    itemDiscounts={itemDiscounts}
                    onToggleItemDiscount={onToggleItemDiscount}
                    itemVatSettings={itemVatSettings}
                    onToggleItemVat={onToggleItemVat}
                    onUpdateItemVatPercentage={onUpdateItemVatPercentage}
                    isLoading={isLoading}
                    // storeCurrency={storeCurrency} // No longer needed
                    cartTotal={cartTotal}
                />;
            case 'summary':
                return <SummaryStep
                    cart={cart}
                    itemDiscounts={itemDiscounts}
                    itemVatSettings={itemVatSettings}
                    // storeCurrency={storeCurrency} // No longer needed
                    cartTotal={cartTotal}
                />;
            case 'payment':
                return <PaymentStep
                    paymentMethod={paymentMethod}
                    onPaymentMethodChange={onPaymentMethodChange}
                    mpesaPhoneNumber={mpesaPhoneNumber}
                    onMpesaPhoneNumberChange={onMpesaPhoneNumberChange}
                    isLoading={isLoading}
                    cartTotal={cartTotal}
                    // storeCurrency={storeCurrency} // No longer needed
                />;
            default:
                return null;
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={(open) => { if (!isLoading && !isProcessing) onOpenChange(open); }}>
            <DialogContent className="
                flex flex-col
                sm:max-w-2xl md:max-w-3xl lg:max-w-4xl xl:max-w-5xl
                w-[95vw] sm:w-[90vw]
                h-[90vh] sm:h-auto
                max-h-[90vh]
                p-0
            ">
                <DialogHeader className="flex-shrink-0 p-4 sm:p-6 pb-4 border-b">
                    <div className="flex items-center gap-3 mb-4">
                        <div className="bg-primary/10 p-2 rounded-lg">
                            <ShoppingCart className="h-6 w-6 text-primary" />
                        </div>
                        <div>
                            <DialogTitle className="text-xl sm:text-2xl">Checkout</DialogTitle>
                            <DialogDescription className="text-sm sm:text-base">
                                Complete your purchase
                            </DialogDescription>
                        </div>
                    </div>

                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 sm:space-x-4">
                            {STEPS.map((step, index) => (
                                <div key={step.id} className="flex items-center">
                                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${index <= currentStepIndex ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
                                        }`}>
                                        {index + 1}
                                    </div>
                                    <span className={`ml-2 text-sm font-medium hidden sm:inline ${index <= currentStepIndex ? 'text-foreground' : 'text-muted-foreground'
                                        }`}>
                                        {step.name}
                                    </span>
                                    {index < STEPS.length - 1 && (
                                        <div className={`w-8 sm:w-12 h-0.5 mx-2 sm:mx-4 transition-colors ${index < currentStepIndex ? 'bg-primary' : 'bg-muted'
                                            }`} />
                                    )}
                                </div>
                            ))}
                        </div>
                        <Badge variant="secondary" className="text-xs sm:text-sm">
                            {cart.length} {cart.length === 1 ? 'item' : 'items'}
                        </Badge>
                    </div>
                </DialogHeader>

                <div className="flex-1 overflow-y-auto min-h-0">
                    {cart.length === 0 ? (
                        <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                            <ShoppingCart className="h-16 w-16 text-muted-foreground/30 mb-4" />
                            <h3 className="text-lg font-semibold">Your Cart is Empty</h3>
                            <p className="text-muted-foreground mb-4">Add products to your cart to get started.</p>
                            <Button variant="outline" onClick={() => onOpenChange(false)}>Continue Shopping</Button>
                        </div>
                    ) : (
                        renderStepContent()
                    )}
                </div>

                {saleStatusMessage && (
                    <div className="px-4 sm:px-6 py-2">
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <p className="text-sm text-blue-800">{saleStatusMessage}</p>
                        </div>
                    </div>
                )}

                <div className="flex-shrink-0 border-t p-4 sm:p-6 bg-background">
                    <div className="flex flex-col sm:flex-row justify-between gap-4">
                        <div className="flex gap-2">
                            {currentStepIndex > 0 && (
                                <Button variant="outline" onClick={handlePrevious} disabled={isLoading || isProcessing} className="flex-1 sm:flex-none">
                                    <ArrowLeft className="h-4 w-4 mr-2" />
                                    Previous
                                </Button>
                            )}
                        </div>
                        <div className="flex gap-2">
                            {currentStep !== 'payment' ? (
                                <Button onClick={handleNext} disabled={!canProceed || isLoading} className="flex-1 sm:flex-none">
                                    Next
                                    <ArrowRight className="h-4 w-4 ml-2" />
                                </Button>
                            ) : (
                                <Button onClick={handleCheckout} disabled={!canProceed || isLoading || isProcessing} className="flex-1 sm:flex-none">
                                    {isProcessing ? (
                                        <>
                                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                                            Processing...
                                        </>
                                    ) : (
                                        <>
                                            <Receipt className="h-4 w-4 mr-2" />
                                            Complete Purchase
                                        </>
                                    )}
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};