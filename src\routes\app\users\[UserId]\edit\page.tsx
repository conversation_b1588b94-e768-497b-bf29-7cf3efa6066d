"use client";

import { useState, useEffect } from "react";
// import { useRouter, useParams } from "next/navigation";
import { StaffForm } from "@/routes/app/users/StaffForm";
import type { NewStaff } from "@/lib/api/users/models";
import { getUser, updateUserRole } from "@/lib/api/users/service";
import Cookies from "js-cookie";
import { Link, useNavigate, useParams } from "react-router";;
import { Button } from "@/components/ui/button";



export default function EditStaffPage() {
  const router = useNavigate();
  // const { staffId } = useParams();
  // const staffId = params.UserId;
  const [initialData, setInitialData] = useState<NewStaff | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [isNotFound, setIsNotFound] = useState<boolean>(false);
  const [staffName, setStaffName] = useState<string>("");

  const token = Cookies.get("auth_token");
  const store_id = Number(Cookies.get("active_store"));
  const userId = Number(useParams().UserId);

  useEffect(() => {
    const fetchData = async () => {
      if (!token || !store_id) {
        setError(new Error("Authentication token or store ID is missing. Please log in again."));
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      setError(null);
      setIsNotFound(false);

      try {
        const staff = await getUser(token, store_id, userId);
        setInitialData(staff);
        setStaffName(staff.name);
      } catch (err: any) {
        if (err.message === "Staff not found") {
          setIsNotFound(true);
        } else {
          setError(err);
        }
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [token, store_id, userId]);

  const handleSubmit = async (data: NewStaff) => {
    if (!token || !store_id) {
      throw new Error("Authentication token or store ID is missing.");
    }

    // await updateUser(token, store_id, userId, data);
    await updateUserRole(token, store_id, userId, data?.user_defined_role_id);
    router("/app/users");
  };

  const handleCancel = () => {
    router(-1);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading staff details for editing...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Staff Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/users">
              <Button variant="link">Return to Staff List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (isNotFound) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Staff Not Found</h2>
          <p className="text-gray-600 text-center">The staff with ID ({userId}) could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/users">
              <Button variant="link">Return to Staff List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!initialData) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <p className="text-gray-600">Staff data could not be prepared for editing.</p>
        <Link to="/app/users" className="ml-4">
          <Button variant="link">Return to Staff List</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link to="/app/users" className="hover:text-blue-600">Staff</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Edit: {staffName || `Staff ${userId}`}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6 flex justify-center">
        <div className="w-full max-w-2xl">
          <StaffForm
            initialData={initialData}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            isEditing={true}
          />
        </div>
      </div>
    </div>
  );
}