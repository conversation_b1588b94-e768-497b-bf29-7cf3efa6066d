{"name": "storeyako_frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3000", "dev-https": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fontsource/inter": "^5.2.8", "@fontsource/lusitana": "^5.2.8", "@fontsource/roboto": "^5.2.8", "@hookform/resolvers": "^5.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "^1.1.12", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.8", "@react-pdf/renderer": "^4.3.1", "@reduxjs/toolkit": "^2.9.0", "@tailwindcss/vite": "^4.1.14", "@tanstack/react-table": "^8.21.3", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "country-data-list": "^1.5.5", "date-fns": "^4.1.0", "firebase": "^12.3.0", "framer-motion": "^12.23.22", "js-cookie": "^3.0.5", "lucide-react": "^0.544.0", "motion": "^12.23.22", "next-themes": "^0.4.6", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-google-recaptcha-v3": "^1.11.0", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.64.0", "react-hot-toast": "^2.6.0", "react-qr-barcode-scanner": "^2.1.15", "react-redux": "^9.2.0", "react-router": "^7.9.3", "react-router-dom": "^7.9.3", "recharts": "^3.2.1", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.14", "uuid": "^13.0.0", "zod": "^4.1.11"}, "devDependencies": {"@eslint/js": "^9.36.0", "@types/js-cookie": "^3.0.6", "@types/node": "^24.6.0", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react": "^5.0.4", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "tw-animate-css": "^1.4.0", "typescript": "5.9.3", "typescript-eslint": "^8.45.0", "vite": "^7.1.7", "vite-plugin-mkcert": "^1.17.9"}}