import { BASE_URL } from "@/routes/configs/constants";
import type { Item, ItemSchema } from "./models";

export async function fetchItems(
  authToken: string,
  storeId: number,
): Promise<Item[]> {
  const url = `${BASE_URL}/items/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch items: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching items:", error);
    throw error;
  }
}

export async function createItems(
  authToken: string,
  storeId: number,
  payload: ItemSchema[],
): Promise<Item> {
  const url = `${BASE_URL}/items/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Failed to create item: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating item:", error);
    throw error;
  }
}

export async function fetchItem(authToken: string, storeId: number, itemId: number): Promise<Item> {
  const url = `${BASE_URL}/items/${storeId}/${itemId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch item: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching item:", error);
    throw error;
  }
}

export async function fetchItemByBarcode(authToken: string, storeId: number, barcode: string): Promise<Item> {
  const url = `${BASE_URL}/items/${storeId}/barcode/${barcode}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch item by barcode: ${response.statusText}`);
    }

    return await response.json();
  }
  catch (error) {
    console.error("Error fetching item by barcode:", error);
    throw error;
  }
}

export async function updateItem(authToken: string, storeId: number, itemId: number, payload: ItemSchema): Promise<Item> {
  const url = `${BASE_URL}/items/${storeId}/${itemId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Failed to update item: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating item:", error);
    throw error;
  }

}

export async function deleteItem(authToken: string, storeId: number, itemId: number): Promise<void> {
  const url = `${BASE_URL}/items/${storeId}/${itemId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });

    if (response.status !== 204) {
      throw new Error(`Failed to delete item: ${response.statusText}`);
    }

  } catch (error) {
    console.error("Error deleting item:", error);
    throw error;
  }
}

// deleted multiple items
// TODO: should be a bulk delete & db transaction
export async function deleteItems(authToken: string, storeId: number, itemIds: number[]): Promise<void> {
  const url = `${BASE_URL}/items/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
      body: JSON.stringify({ itemIds }),
    });

    if (response.status !== 204) {
      throw new Error(`Failed to delete items: ${response.statusText}`);
    }
  } catch (error) {
    console.error("Error deleting items:", error);
    throw error;
  }
}


export async function createItemsFromMatrices(authToken: string, storeId: number, payload: ItemSchema[]): Promise<Item[]> {
  const url = `${BASE_URL}/items/matrices/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Failed to create items from matrices: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error creating items from matrices:", error);
    throw error;
  }
}

export async function updateMultipleItems(authToken: string, storeId: number, payload: Item[]): Promise<Item[]> {
  const url = `${BASE_URL}/items/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`Failed to update items: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error updating items:", error);
    throw error;
  }
}
