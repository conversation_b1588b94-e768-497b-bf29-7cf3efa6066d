export interface Item {
  id: number;
  url: string;
  store_id: number;
  name: string;
  image?: string;
  quantity: number;
  brand: number;
  category: number;
  default_cost?: number;
  default_price: number;
  has_discount: boolean;
  discount?: number | null;
  date_created: string;
  date_updated?: string | null;
  tags: string[];
  vendor_id?: number | null;
  is_variant: boolean;
  parent_item_id?: number | null;
  sku?: string | null;
  description?: string | null;
  notes?: string | null;
  barcode?: string | null;
  vat_included: boolean;
  vat_percentage: number;
}

export interface ItemSchema {
  name: string;
  quantity: number;
  default_cost?: number;
  default_price: number;
  brand: number;
  has_discount: boolean;
  date_created: string;
  tags: string[];
  category: number;
  store_id: number;
  vendor_id?: number | null;
  image?: File | string;
  description?: string | null;
  sku?: string;
  barcode?: string;
  discount?: number | null;
  notes?: string | null;
  is_variant: boolean;
  parent_item_id?: number | null;
  vat_included: boolean;
  vat_percentage: number;
}