"use client";

import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alog<PERSON>lose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import type { BrandSchema } from "@/lib/api/brands/models";
import { Textarea } from "@/components/ui/textarea";
import { createBrand } from "@/lib/api/brands/service";
import Cookies from "js-cookie";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router";

const brandFormSchema = z.object({
  name: z.string().min(2, "Name must be at least 2 characters"),
  description: z.string().optional(),
});

type BrandFormData = z.infer<typeof brandFormSchema>;

interface BrandModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (data: BrandSchema) => Promise<void>;
}

const BrandModal: React.FC<BrandModalProps> = ({
  open,
  onOpenChange,
}) => {
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [success, setSuccess] = React.useState(false);

  const form = useForm<BrandFormData>({
    resolver: zodResolver(brandFormSchema),
    defaultValues: {
      name: "",
      description: "",
    },
  });

  const token = Cookies.get("token");
  const store_id = Number(Cookies.get("store_id"));

  const handleSubmit = async (data: BrandFormData) => {
    setIsSubmitting(true);
    setError(null);
    setSuccess(false);

    try {
      if (!token || !store_id) {
        throw new Error("Authentication token or store ID is missing.");
      }

      const payload: BrandSchema = {
        name: data.name,
        description: data.description || "",
      };

      await createBrand(token, store_id, payload);
      setSuccess(true);
      form.reset();
      onOpenChange(false);
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Brand</DialogTitle>
        </DialogHeader>

        {error && <AlertError message={error} />}
        {success && <AlertSuccess message="Brand added successfully!" />}

        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              {...form.register("name")}
              placeholder="Brand name"
              className={form.formState.errors.name ? "border-red-500" : ""}
            />
            {form.formState.errors.name && (
              <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...form.register("description")}
              placeholder="Brand description"
            />
          </div>

          <DialogFooter className="sm:justify-start">
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Brand"}
            </Button>
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

// Next.js page component
const CreateBrandPage = () => {
  const router = useNavigate();
  // Modal is open by default
  const [open, setOpen] = React.useState(true);

  const handleOpenChange = (isOpen: boolean) => {
    setOpen(isOpen);
    if (!isOpen) {
      // Navigate back to brands list or parent route
      router(-1);
    }
  };

  return (
    <BrandModal open={open} onOpenChange={handleOpenChange} onSubmit={async () => {}} />
  );
};

export default CreateBrandPage;
