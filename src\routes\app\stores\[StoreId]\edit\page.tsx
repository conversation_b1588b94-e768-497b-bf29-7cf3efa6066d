"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import { StoreForm } from "@/routes/app/stores/StoreForm";
import type { Store, StoreSchema } from "@/lib/api/retailStores/models";
import { getStore, updateStore } from "@/lib/api/retailStores/service"; // Import fetchStore and updateStore
import { useParams } from "react-router";


export default function EditStorePage() {
  const [initialData, setInitialData] = useState<StoreSchema | null>(null);

  const token = Cookies.get("auth_token");
  const storeId = Number(useParams().storeId);
  const storeUrl = Cookies.get("active_store_url");


  useEffect(() => {
    if (!token) return;

    const fetchData = async () => {
      try {
        const store: Store = await getStore(storeId, token);
        const storeSchema: StoreSchema = {
          ...store,
        };
        setInitialData(storeSchema);
      } catch (error) {
        console.error("Failed to fetch store:", error);
      }
    };

    fetchData();
  }, [token, storeId]);

  const handleSubmit = async (data: StoreSchema) => {
    if (!token) {
      throw new Error("Authentication token or store ID is missing.");
    }
    await updateStore(storeId, data, token);
  };

  if (!initialData) {
    return <div>Loading...</div>;
  }

  return (
    <StoreForm
      initialData={initialData}
      onSubmit={handleSubmit}
      isEditing
      storeUrl={storeUrl}
    />
  );
}