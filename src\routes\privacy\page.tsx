
export default function PrivacyPolicyPage() {
  return (
    <main className="container mx-auto px-4 py-16 max-w-3xl">
      <h1 className="text-3xl font-bold mb-8 text-center">Privacy Policy</h1>
      <p className="mb-6 text-sm text-muted-foreground text-center">Last updated: May 6, 2025</p>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">1. Introduction</h2>
        <p>
          StoreYako POS is committed to protecting your privacy. This Privacy Policy explains how we collect, use, and safeguard your information when you use our web app during beta testing.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">2. Information We Collect</h2>
        <ul className="list-disc pl-6">
          <li><strong>Personal Information:</strong> Such as your name, email address, and business details when you register or contact support.</li>
          <li><strong>Usage Data:</strong> Information about how you interact with the app, including device and browser type.</li>
          <li><strong>Payment Data:</strong> Transaction details processed through integrated payment providers (e.g., M-Pesa).</li>
        </ul>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">3. How We Use Your Information</h2>
        <ul className="list-disc pl-6">
          <li>To provide and maintain the StoreYako POS service.</li>
          <li>To improve the app based on user feedback and usage patterns.</li>
          <li>To communicate with you about updates, issues, or support.</li>
          <li>To comply with legal obligations.</li>
        </ul>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">4. Data Security</h2>
        <p>
          We implement reasonable security measures to protect your data. However, please note that no method of transmission over the internet is 100% secure, especially during beta testing.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">5. Sharing of Information</h2>
        <p>
          We do not sell your personal information. We may share information with trusted third-party service providers as necessary to operate the app (e.g., payment processors), or when required by law.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">6. Children’s Privacy</h2>
        <p>
          StoreYako POS is not intended for children under 18. We do not knowingly collect data from children.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">7. Changes to This Policy</h2>
        <p>
          We may update this Privacy Policy from time to time. We encourage you to review it periodically.
        </p>
      </section>
      <section className="mb-8">
        <h2 className="text-xl font-semibold mb-2">8. Contact</h2>
        <p>
          If you have any questions or concerns, please contact us at <a href="mailto:<EMAIL>" className="text-primary underline"><EMAIL></a>.
        </p>
      </section>
    </main>
  );
}
