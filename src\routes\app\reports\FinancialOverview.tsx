
// import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ReferenceLine } from 'recharts';
// import { type ChartConfig, ChartContainer, ChartTooltip, ChartLegend, ChartLegendContent } from '@/components/ui/chart';
// import type { MonthlyFinancialData } from '@/lib/api/reports/models';

// interface FinancialOverviewProps {
//     financialData: MonthlyFinancialData[] | null;
// }

// const chartConfig = {
//   profit: {
//     label: "Profit",
//     color: "hsl(174, 62%, 47%)", // Similar to rgb(75, 192, 192)
//   },
//   loss: {
//     label: "Loss",
//     color: "hsl(348, 83%, 47%)", // Similar to rgb(255, 99, 132)
//   },
// } satisfies ChartConfig;

// const FinancialOverview = ({ financialData }: FinancialOverviewProps) => {
//   if (!financialData) {
//     return <div>Loading... or Error</div>;
//   }

//   // Transform data for Recharts
//   const chartData = financialData.map((item) => ({
//     month: item.month,
//     // Format month for better display (e.g., "2024-01" -> "Jan 2024")
//     displayMonth: new Date(item.month + '-01').toLocaleDateString('en-US', { 
//       month: 'short', 
//       year: 'numeric' 
//     }),
//     profit: item.profit,
//     loss: item.loss,
//     // Calculate net for potential future use
//     net: item.profit - item.loss,
//   }));

//   // Calculate summary statistics
//   const totalProfit = chartData.reduce((sum, item) => sum + item.profit, 0);
//   const totalLoss = chartData.reduce((sum, item) => sum + item.loss, 0);
//   const netTotal = totalProfit - totalLoss;

//   return (
//     <div className="space-y-4">
//       <div>
//         <h2 className="text-lg font-semibold mb-2">Financial Overview</h2>
//         <div className="grid grid-cols-3 gap-4 mb-4">
//           <div className="text-center p-2 rounded-lg bg-green-50 dark:bg-green-950/20 border">
//             <div className="text-sm text-muted-foreground">Total Profit</div>
//             <div className="text-lg font-semibold text-green-600 dark:text-green-400">
//               ${totalProfit.toLocaleString()}
//             </div>
//           </div>
//           <div className="text-center p-2 rounded-lg bg-red-50 dark:bg-red-950/20 border">
//             <div className="text-sm text-muted-foreground">Total Loss</div>
//             <div className="text-lg font-semibold text-red-600 dark:text-red-400">
//               ${totalLoss.toLocaleString()}
//             </div>
//           </div>
//           <div className="text-center p-2 rounded-lg bg-blue-50 dark:bg-blue-950/20 border">
//             <div className="text-sm text-muted-foreground">Net Total</div>
//             <div className={`text-lg font-semibold ${
//               netTotal >= 0 
//                 ? 'text-green-600 dark:text-green-400' 
//                 : 'text-red-600 dark:text-red-400'
//             }`}>
//               ${netTotal.toLocaleString()}
//             </div>
//           </div>
//         </div>
//       </div>
      
//       <ChartContainer config={chartConfig} className="h-[250px] w-full">
//         <LineChart
//           data={chartData}
//           margin={{
//             top: 20,
//             right: 30,
//             left: 20,
//             bottom: 20,
//           }}
//         >
//           <XAxis
//             dataKey="displayMonth"
//             tickLine={false}
//             axisLine={false}
//             className="text-xs"
//             angle={-45}
//             textAnchor="end"
//             height={60}
//           />
//           <YAxis
//             tickLine={false}
//             axisLine={false}
//             tickFormatter={(value) => `$${value.toLocaleString()}`}
//             className="text-xs"
//           />
//           <ReferenceLine y={0} stroke="hsl(var(--muted-foreground))" strokeDasharray="2 2" />
//           <Line
//             type="monotone"
//             dataKey="profit"
//             stroke="var(--color-profit)"
//             strokeWidth={3}
//             dot={{
//               fill: "var(--color-profit)",
//               strokeWidth: 2,
//               r: 4,
//             }}
//             activeDot={{
//               r: 6,
//               fill: "var(--color-profit)",
//               stroke: "var(--color-profit)",
//               strokeWidth: 2,
//             }}
//           />
//           <Line
//             type="monotone"
//             dataKey="loss"
//             stroke="var(--color-loss)"
//             strokeWidth={3}
//             dot={{
//               fill: "var(--color-loss)",
//               strokeWidth: 2,
//               r: 4,
//             }}
//             activeDot={{
//               r: 6,
//               fill: "var(--color-loss)",
//               stroke: "var(--color-loss)",
//               strokeWidth: 2,
//             }}
//           />
//           <ChartTooltip
//             content={({ active, payload, label }) => {
//               if (active && payload && payload.length) {
//                 const data = payload[0].payload;
//                 return (
//                   <div className="rounded-lg border bg-background p-3 shadow-md">
//                     <div className="grid gap-2">
//                       <div className="font-medium">{label}</div>
//                       <div className="grid gap-1">
//                         <div className="flex items-center justify-between gap-2">
//                           <div className="flex items-center gap-2">
//                             <div className="w-3 h-3 rounded-full" style={{ backgroundColor: 'var(--color-profit)' }} />
//                             <span className="text-sm">Profit:</span>
//                           </div>
//                           <span className="text-sm font-medium text-green-600 dark:text-green-400">
//                             ${data.profit?.toLocaleString()}
//                           </span>
//                         </div>
//                         <div className="flex items-center justify-between gap-2">
//                           <div className="flex items-center gap-2">
//                             <div className="w-3 h-3 rounded-full" style={{ backgroundColor: 'var(--color-loss)' }} />
//                             <span className="text-sm">Loss:</span>
//                           </div>
//                           <span className="text-sm font-medium text-red-600 dark:text-red-400">
//                             ${data.loss?.toLocaleString()}
//                           </span>
//                         </div>
//                         <div className="border-t pt-1 mt-1">
//                           <div className="flex items-center justify-between gap-2">
//                             <span className="text-sm font-medium">Net:</span>
//                             <span className={`text-sm font-bold ${
//                               data.net >= 0 
//                                 ? 'text-green-600 dark:text-green-400' 
//                                 : 'text-red-600 dark:text-red-400'
//                             }`}>
//                               ${data.net?.toLocaleString()}
//                             </span>
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   </div>
//                 );
//               }
//               return null;
//             }}
//           />
//           <ChartLegend content={<ChartLegendContent payload={[]} />} />
//         </LineChart>
//       </ChartContainer>
//     </div>
//   );
// };

// export default FinancialOverview;

import {
  Line,
  LineChart,
  XAxis,
  YAxis,
  ReferenceLine,
  ResponsiveContainer,
} from "recharts";
import {
  type ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartLegend,
  ChartLegendContent,
} from "@/components/ui/chart";
import type { MonthlyFinancialData } from "@/lib/api/reports/models";
import { formatCurrency } from "@/lib/utils/currency";

interface FinancialOverviewProps {
  financialData: MonthlyFinancialData[] | null;
}

const chartConfig = {
  profit: {
    label: "Profit",
    color: "hsl(174, 62%, 47%)",
  },
  loss: {
    label: "Loss",
    color: "hsl(348, 83%, 47%)",
  },
} satisfies ChartConfig;

export default function FinancialOverview({ financialData }: FinancialOverviewProps) {
  if (!financialData) {
    return <div className="text-sm text-muted-foreground">No financial data available</div>;
  }

  const chartData = financialData.map((item) => ({
    month: item.month,
    displayMonth: new Date(item.month + "-01").toLocaleDateString("en-US", {
      month: "short",
      year: "2-digit",
    }),
    profit: item.profit,
    loss: item.loss,
    net: item.profit - item.loss,
  }));

  const totalProfit = chartData.reduce((sum, item) => sum + item.profit, 0);
  const totalLoss = chartData.reduce((sum, item) => sum + item.loss, 0);
  const netTotal = totalProfit - totalLoss;

  return (
    <div className="flex flex-col w-full overflow-hidden">
      <h2 className="text-lg font-semibold mb-3">Financial Overview</h2>

      {/* Summary cards */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-6">
        <div className="text-center p-3 rounded-lg bg-green-50 dark:bg-green-950/20 border">
          <div className="text-xs text-muted-foreground">Total Profit</div>
          <div className="text-base font-semibold text-green-600 dark:text-green-400">
            {formatCurrency(totalProfit)}
          </div>
        </div>

        <div className="text-center p-3 rounded-lg bg-red-50 dark:bg-red-950/20 border">
          <div className="text-xs text-muted-foreground">Total Loss</div>
          <div className="text-base font-semibold text-red-600 dark:text-red-400">
            {formatCurrency(totalLoss)}
          </div>
        </div>

        <div className="text-center p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border">
          <div className="text-xs text-muted-foreground">Net Total</div>
          <div
            className={`text-base font-semibold ${
              netTotal >= 0
                ? "text-green-600 dark:text-green-400"
                : "text-red-600 dark:text-red-400"
            }`}
          >
            {formatCurrency(netTotal)}
          </div>
        </div>
      </div>

      {/* Chart container (fully responsive and clipped correctly) */}
      <div className="flex-1 w-full h-[250px] md:h-[320px] overflow-x-hidden">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 20, right: 20, left: 0, bottom: 20 }}
            >
              <XAxis
                dataKey="displayMonth"
                tickLine={false}
                axisLine={false}
                interval="preserveEnd"
                tick={{ fontSize: 12 }}
              />
              <YAxis
                tickLine={false}
                axisLine={false}
                tickFormatter={(v) => `${formatCurrency(v)}`}
                tick={{ fontSize: 12 }}
              />
              <ReferenceLine y={0} stroke="#ccc" strokeDasharray="3 3" />
              <Line
                type="monotone"
                dataKey="profit"
                stroke="var(--color-profit)"
                strokeWidth={2.5}
                dot={{ r: 4, fill: "var(--color-profit)" }}
              />
              <Line
                type="monotone"
                dataKey="loss"
                stroke="var(--color-loss)"
                strokeWidth={2.5}
                dot={{ r: 4, fill: "var(--color-loss)" }}
              />
              <ChartTooltip />
              <ChartLegend content={<ChartLegendContent payload={[]} />} />
            </LineChart>
          </ResponsiveContainer>
        </ChartContainer>
      </div>
    </div>
  );
}
