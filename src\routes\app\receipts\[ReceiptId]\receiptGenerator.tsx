import React from 'react';

interface Store {
    name: string;
    address?: string;
    phone?: string;
    postal_code?: string;
    country?: string;
    city?: string;
    currency?: string;
}

interface Item {
    name: string;
    amount: number;
    quantity: number;
    store_id: number; // This might not be needed for display on the receipt itself
}

interface ReceiptProps { // Renamed to ReceiptProps for clarity, as Receipt is also a component name
    store: Store;
    total: number;
    salesperson: string;
    items: Item[];
    receipt_number: string;
    id: number; // Not used in the current display logic, but kept for interface consistency
    created_at: string;
}

const Receipt = ({
    store,
    total,
    salesperson,
    items,
    receipt_number,
    created_at
}: ReceiptProps) => {
    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        // Using toLocaleString with options for better control over format
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }) + ' ' + date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
    };

    const formatCurrency = (amount: number) => {
        // Use Intl.NumberFormat for robust currency formatting
        return new Intl.NumberFormat('en-US', {
            style: 'decimal', // Use 'currency' if you want the symbol, but 'decimal' is common for just the amount
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        }).format(amount);
    };

    return (
        // Main container for the receipt, setting a fixed width for print-like appearance
        <div className="font-mono text-xs w-[300px] bg-white p-4 leading-normal shadow-lg rounded-lg mx-auto my-8">
            {/* Store Header */}
            <div className="text-center mb-4 pb-2 border-b border-gray-300">
                {/* Optional: Placeholder for a logo */}
                {/* <img src="https://placehold.co/100x50/000/fff?text=LOGO" alt="Store Logo" className="mx-auto mb-2 rounded" /> */}
                <div className="font-extrabold text-xl mb-1 text-gray-800">{store.name}</div>
                {store.address && <div className="text-gray-600">{store.address}</div>}
                {(store.city || store.postal_code) && (
                    <div className="text-gray-600">
                        {store.city}
                        {store.city && store.postal_code && ', '}
                        {store.postal_code}
                    </div>
                )}
                {store.country && <div className="text-gray-600">{store.country}</div>}
                {store.phone && <div className="text-gray-600">Tel: {store.phone}</div>}
            </div>

            {/* Receipt Info */}
            <div className="border-t border-b border-dashed border-gray-400 py-3 mb-4 text-gray-700">
                <div className="flex justify-between mb-1">
                    <span className="font-semibold">Receipt No:</span>
                    <span>{receipt_number}</span>
                </div>
                <div className="flex justify-between mb-1">
                    <span className="font-semibold">Date:</span>
                    <span>{formatDate(created_at)}</span>
                </div>
                <div className="flex justify-between">
                    <span className="font-semibold">Cashier:</span>
                    <span>{salesperson}</span>
                </div>
            </div>

            {/* Items Header */}
            <div className="mb-2 pb-1 border-b border-black font-bold text-gray-800">
                <div className="grid grid-cols-12 gap-2">
                    <div className="col-span-6">ITEM</div>
                    <div className="col-span-2 text-right">QTY</div>
                    <div className="col-span-4 text-right">AMOUNT</div>
                </div>
            </div>

            {/* Items List */}
            <div className="mb-4">
                {items.map((item, index) => (
                    <div key={index} className="text-gray-800 py-1">
                        <div className="text-sm font-medium mb-0.5">{item.name}</div>
                        <div className="grid grid-cols-12 gap-2 text-xs">
                            <div className="col-span-6"></div> {/* Empty column for alignment */}
                            <div className="col-span-2 text-right">{item.quantity} x</div>
                            <div className="col-span-4 text-right">
                                {formatCurrency(item.amount)}
                            </div>
                        </div>
                        {/* Dotted line separator for items, except for the last one */}
                        {index < items.length - 1 && (
                            <div className="border-b border-dotted border-gray-300 mt-2"></div>
                        )}
                    </div>
                ))}
            </div>

            {/* Total */}
            <div className="border-t border-b border-black py-3 mb-4">
                <div className="flex justify-between font-extrabold text-lg text-gray-900">
                    <div>TOTAL</div>
                    <div>{store.currency || '$'} {formatCurrency(total)}</div>
                </div>
            </div>

            {/* Footer */}
            <div className="text-center text-gray-700 mt-4">
                <div className="font-semibold text-sm mb-1">Thank you for your purchase!</div>
                <div className="text-xs">Please come again</div>
                {/* Optional: QR Code Placeholder */}
                {/* <img src="https://placehold.co/80x80/000/fff?text=QR" alt="QR Code" className="mx-auto mt-4 rounded" /> */}
            </div>
        </div>
    );
};

export default Receipt;
