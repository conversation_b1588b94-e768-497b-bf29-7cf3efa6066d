import { BASE_URL } from "@/routes/configs/constants";
import type { MatrixAttributesValues } from "./models";

interface MatrixAttributeValuesSchema {
  position: number,
  item_id: number,
  attribute_value_ids: number
}

export async function createMatrixAttributeValues(
  authToken: string,
  storeId: number,
  payload: MatrixAttributeValuesSchema[]
): Promise<void> {
  const url = `${BASE_URL}/items/attributes/values/matrices/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });
    if (response.status !== 201) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create attribute value: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error creating attribute value:", error);
    throw error;
  }
}

export async function fetchMatrixAttributeValues(
  authToken: string,
  storeId: number,
  matrixId: number,
): Promise<MatrixAttributesValues[]> {
  const url = `${BASE_URL}/items/attributes/values/matrices/${storeId}/${matrixId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch attribute values: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching attribute values:", error);
    throw error;
  }
}


export async function updateMatrixAttributeValues(
  authToken: string,
  storeId: number,
  matrixId: number,
  payload: MatrixAttributeValuesSchema[]
): Promise<void> {
  const url = `${BASE_URL}/items/attributes/values/matrices/${storeId}/${matrixId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(payload),
    });
    if (response.status !== 200) {
      const errorMessage = await response.json();
      throw new Error(`Failed to update attribute values: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error updating attribute values:", error);
    throw error;
  }
}