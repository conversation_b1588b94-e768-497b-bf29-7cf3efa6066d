import { BASE_URL } from "@/routes/configs/constants";
import  type { Supplier, SupplierSchema } from "./models";

export async function fetchSuppliers(authToken: string, storeId: number)
  : Promise<Supplier[]> {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch suppliers");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

// TODO: #1 create a handler to fetch all single supplier


export async function createSupplier(authToken: string, storeId: number, supplier: SupplierSchema) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(supplier),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to create supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getSupplier(authToken: string, storeId: number,supplierId: number) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}/${supplierId}`, {
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to fetch supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}


export async function updateSupplier(authToken: string, storeId: number, supplierId: number, supplier: SupplierSchema) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}/${supplierId}`, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(supplier),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteSupplier(authToken: string, storeId: number, supplierId: number) {
  try {
    const response = await fetch(`${BASE_URL}/suppliers/${storeId}/${supplierId}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${authToken}`,
        "Content-Type": "application/json",
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to delete supplier");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}
