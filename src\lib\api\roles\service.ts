import { BASE_URL } from "@/routes/configs/constants";
import type { RoleWithPermissions, UsersWithRolesResponse, UserWithRoleDetails } from "@/lib/api/roles/models";
import Cookies from "js-cookie";

// Legacy function (keeping for backward compatibility)
export async function fetchRolesWithPermissions(
  authToken: string
): Promise<RoleWithPermissions[]> {
  const url = `${BASE_URL}/admin/roles`;
  const storeId = Number(Cookies.get("active_store"));
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch roles: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error: any) {
    console.error("Error fetching roles:", error);
    throw error;
  }
}

// New function for fetching users with detailed role information
export async function fetchUsersWithRoles(
  storeId: number,
  authToken: string
): Promise<UsersWithRolesResponse> {
  const url = `${BASE_URL}/admin/users/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch users with roles: ${errorMessage.error || response.statusText}`);
    }
    return await response.json();
  } catch (error: any) {
    console.error("Error fetching users with roles:", error);
    throw error;
  }
}

// Helper function to get unique permissions across all users
export function getAllUniquePermissions(users: UserWithRoleDetails[]): string[] {
  const permissionsSet = new Set<string>();
  users.forEach(user => {
    user.all_permissions.forEach(permission => {
      permissionsSet.add(permission);
    });
  });
  return Array.from(permissionsSet).sort();
}

// Helper function to get unique groups across all users
export function getAllUniqueGroups(users: UserWithRoleDetails[]) {
  const groupsMap = new Map();
  users.forEach(user => {
    user.groups.forEach(group => {
      if (!groupsMap.has(group.group_id)) {
        groupsMap.set(group.group_id, group);
      }
    });
  });
  return Array.from(groupsMap.values());
}

// Helper function to get role statistics
export function getRoleStatistics(users: UserWithRoleDetails[]) {
  const roleCount = new Map<string, number>();
  users.forEach(user => {
    const role = user.user_defined_role;
    roleCount.set(role, (roleCount.get(role) || 0) + 1);
  });

  const total = users.length;
  return Array.from(roleCount.entries()).map(([role, count]) => ({
    role,
    count,
    percentage: Math.round((count / total) * 100)
  }));
}