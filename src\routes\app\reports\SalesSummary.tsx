import { Line, LineChart, Bar, <PERSON><PERSON>hart, XAxis, YAxis } from 'recharts';
import { type ChartConfig, ChartContainer, ChartTooltip, ChartLegend, ChartLegendContent } from '@/components/ui/chart';
import { TrendingUp, TrendingDown, Target, ShoppingCart } from 'lucide-react';
import type { DailyTotalSales, ProductPerformance } from "@/lib/api/reports/models";
import { formatCurrency } from '@/lib/utils/currency';

const monthLabels = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const monthAbbreviations = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

interface SalesSummaryProps {
  salesData: DailyTotalSales[] | null;
  productData: ProductPerformance[] | null;
}

const salesChartConfig = {
  sales: {
    label: "Sales",
    color: "hsl(210, 100%, 60%)", // Similar to rgb(53, 162, 235)
  },
  target: {
    label: "Target",
    color: "hsl(348, 83%, 47%)", // Similar to rgb(255, 99, 132)
  },
} satisfies ChartConfig;

const categoryChartConfig = {
  quantity: {
    label: "Sales Quantity",
    color: "hsl(174, 62%, 47%)", // Similar to rgba(75, 192, 192)
  },
} satisfies ChartConfig;

const SalesSummary = ({ salesData, productData }: SalesSummaryProps) => {
    if (!salesData || !productData) {
        return <div>Loading... or Error</div>;
    }

    // Process monthly sales data
    const salesByMonth = Array(12).fill(0);
    salesData.forEach(({ date, total }) => {
        const monthIndex = new Date(date).getMonth();
        salesByMonth[monthIndex] += total;
    });

    // Transform data for line chart
    const lineChartData = monthLabels.map((month, index) => ({
        month: monthAbbreviations[index],
        fullMonth: month,
        sales: salesByMonth[index],
        target: 1000,
        variance: salesByMonth[index] - 1000,
        isAboveTarget: salesByMonth[index] >= 1000,
    }));

    // Process category data and sort by quantity
    const sortedProductData = [...productData]
        .sort((a, b) => b.quantity - a.quantity)
        .slice(0, 10); // Show top 10 categories

    const barChartData = sortedProductData.map(item => ({
        category: item.category.length > 15 
            ? `${item.category.slice(0, 15)}...` 
            : item.category,
        fullCategory: item.category,
        quantity: item.quantity,
    }));

    // Calculate summary statistics
    const totalSales = salesByMonth.reduce((sum, val) => sum + val, 0);
    const totalTarget = 12000; // 12 months * 1000 target
    const averageMonthlySales = totalSales / 12;
    const targetAchievement = (totalSales / totalTarget) * 100;
    const monthsAboveTarget = salesByMonth.filter(sales => sales >= 1000).length;

    const totalCategorySales = productData.reduce((sum, item) => sum + item.quantity, 0);
    const topCategory = productData.reduce((prev, current) => 
        prev.quantity > current.quantity ? prev : current
    );

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-blue-600" />
                    Overall Sales Performance
                </h2>

                {/* Summary Cards */}
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 mb-6">
                    <div className="p-3 rounded-lg bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800">
                        <div className="flex items-center gap-2 mb-1">
                            <ShoppingCart className="h-4 w-4 text-blue-600" />
                            <div className="text-xs text-blue-600 dark:text-blue-400">Total Sales</div>
                        </div>
                        <div className="text-lg font-bold text-blue-700 dark:text-blue-300">
                           {formatCurrency(totalSales)}
                        </div>
                        <div className="text-xs text-blue-600 dark:text-blue-400">
                            Avg: {formatCurrency(averageMonthlySales)}/month
                        </div>
                    </div>

                    <div className="p-3 rounded-lg bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800">
                        <div className="flex items-center gap-2 mb-1">
                            <Target className="h-4 w-4 text-green-600" />
                            <div className="text-xs text-green-600 dark:text-green-400">Target Achievement</div>
                        </div>
                        <div className="text-lg font-bold text-green-700 dark:text-green-300">
                            {targetAchievement.toFixed(1)}%
                        </div>
                        <div className="text-xs text-green-600 dark:text-green-400">
                            {monthsAboveTarget}/12 months above target
                        </div>
                    </div>

                    <div className="p-3 rounded-lg bg-purple-50 dark:bg-purple-950/20 border border-purple-200 dark:border-purple-800">
                        <div className="flex items-center gap-2 mb-1">
                            <TrendingUp className="h-4 w-4 text-purple-600" />
                            <div className="text-xs text-purple-600 dark:text-purple-400">Top Category</div>
                        </div>
                        <div className="text-sm font-bold text-purple-700 dark:text-purple-300 truncate">
                            {topCategory.category}
                        </div>
                        <div className="text-xs text-purple-600 dark:text-purple-400">
                            {topCategory.quantity.toLocaleString()} units
                        </div>
                    </div>

                    <div className="p-3 rounded-lg bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800">
                        <div className="flex items-center gap-2 mb-1">
                            <ShoppingCart className="h-4 w-4 text-orange-600" />
                            <div className="text-xs text-orange-600 dark:text-orange-400">Total Units</div>
                        </div>
                        <div className="text-lg font-bold text-orange-700 dark:text-orange-300">
                            {totalCategorySales.toLocaleString()}
                        </div>
                        <div className="text-xs text-orange-600 dark:text-orange-400">
                            All categories combined
                        </div>
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Monthly Sales Chart */}
                <div>
                    <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                        <TrendingUp className="h-4 w-4" />
                        Monthly Sales vs Target
                    </h3>
                    <ChartContainer config={salesChartConfig} className="h-[300px] w-full">
                        <LineChart
                            data={lineChartData}
                            margin={{
                                top: 20,
                                right: 30,
                                left: 20,
                                bottom: 20,
                            }}
                        >
                            <XAxis
                                dataKey="month"
                                tickLine={false}
                                axisLine={false}
                                className="text-xs"
                            />
                            <YAxis
                                tickLine={false}
                                axisLine={false}
                                tickFormatter={(value) => `${formatCurrency(value)}`}
                                className="text-xs"
                            />
                            <Line
                                type="monotone"
                                dataKey="sales"
                                stroke="var(--color-sales)"
                                strokeWidth={3}
                                dot={{
                                    fill: "var(--color-sales)",
                                    strokeWidth: 2,
                                    r: 4,
                                }}
                                activeDot={{
                                    r: 6,
                                    fill: "var(--color-sales)",
                                }}
                            />
                            <Line
                                type="monotone"
                                dataKey="target"
                                stroke="var(--color-target)"
                                strokeWidth={2}
                                strokeDasharray="5 5"
                                dot={false}
                            />
                            <ChartTooltip
                                content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                        const data = payload[0].payload;
                                        return (
                                            <div className="rounded-lg border bg-background p-3 shadow-md">
                                                <div className="grid gap-2">
                                                    <div className="font-medium">{data.fullMonth}</div>
                                                    <div className="grid gap-1">
                                                        <div className="flex items-center justify-between gap-2">
                                                            <div className="flex items-center gap-2">
                                                                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: 'var(--color-sales)' }} />
                                                                <span className="text-sm">Sales:</span>
                                                            </div>
                                                            <span className="text-sm font-medium">
                                                                {formatCurrency(data.sales) }
                                                            </span>
                                                        </div>
                                                        <div className="flex items-center justify-between gap-2">
                                                            <div className="flex items-center gap-2">
                                                                <div className="w-3 h-3 rounded-full" style={{ backgroundColor: 'var(--color-target)' }} />
                                                                <span className="text-sm">Target:</span>
                                                            </div>
                                                            <span className="text-sm font-medium">
                                                                {formatCurrency(data.target) }
                                                            </span>
                                                        </div>
                                                        <div className="border-t pt-1 mt-1">
                                                            <div className="flex items-center justify-between gap-2">
                                                                <span className="text-sm">Variance:</span>
                                                                <span className={`text-sm font-bold flex items-center gap-1 ${
                                                                    data.variance >= 0
                                                                        ? 'text-green-600 dark:text-green-400'
                                                                        : 'text-red-600 dark:text-red-400'
                                                                }`}>
                                                                    {data.variance >= 0 ? (
                                                                        <TrendingUp className="h-3 w-3" />
                                                                    ) : (
                                                                        <TrendingDown className="h-3 w-3" />
                                                                    )}
                                                                    { formatCurrency( Math.abs(data.variance))}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    }
                                    return null;
                                }}
                            />
                            <ChartLegend content={<ChartLegendContent payload={[]} />} />
                        </LineChart>
                    </ChartContainer>
                </div>

                {/* Category Sales Chart */}
                <div>
                    <h3 className="text-md font-medium mb-3 flex items-center gap-2">
                        <ShoppingCart className="h-4 w-4" />
                        Top Category Performance
                    </h3>
                    <ChartContainer config={categoryChartConfig} className="h-[300px] w-full">
                        <BarChart
                            data={barChartData}
                            margin={{
                                top: 20,
                                right: 30,
                                left: 20,
                                bottom: 60,
                            }}
                        >
                            <XAxis
                                dataKey="category"
                                tickLine={false}
                                axisLine={false}
                                className="text-xs"
                                angle={-45}
                                textAnchor="end"
                                height={80}
                            />
                            <YAxis
                                tickLine={false}
                                axisLine={false}
                                tickFormatter={(value) => value.toLocaleString()}
                                className="text-xs"
                            />
                            <Bar
                                dataKey="quantity"
                                fill="var(--color-quantity)"
                                radius={[4, 4, 0, 0]}
                                className="cursor-pointer"
                            />
                            <ChartTooltip
                                content={({ active, payload }) => {
                                    if (active && payload && payload.length) {
                                        const data = payload[0].payload;
                                        return (
                                            <div className="rounded-lg border bg-background p-3 shadow-md">
                                                <div className="grid gap-2">
                                                    <div className="font-medium">{data.fullCategory}</div>
                                                    <div className="flex items-center justify-between gap-2">
                                                        <span className="text-sm text-muted-foreground">Units Sold:</span>
                                                        <span className="text-sm font-bold text-teal-600 dark:text-teal-400">
                                                            {data.quantity?.toLocaleString()}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        );
                                    }
                                    return null;
                                }}
                            />
                        </BarChart>
                    </ChartContainer>
                </div>
            </div>
        </div>
    );
};

export default SalesSummary;