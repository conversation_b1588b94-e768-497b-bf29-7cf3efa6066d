export interface ItemMatrixSchema {
  store_id: number;
  name: string;
  description: string;
  matrix_type: string;
  category: number;
  brand: number;
  vendor_id: number;
  default_price: number;
  default_currency?: string;
  msrp?: number;
  default_cost: number;
  discount?: number;
  has_discount?: boolean;
  vat_included?: boolean;
  vat_percentage?: number;
}

export interface ItemMatrix {
  id: number;
  url: string;
  store_id: number;
  description: string;
  name: string;
  matrix_type: string;
  category: number;
  brand: number;
  vendor_id: number;
  default_price: number;
  date_created: string;
  default_currency: string;
  date_updated?: string;
  msrp?: number;
  default_cost: number;
  discount?: number;
  has_discount: boolean;
  vat_included: boolean;
  vat_percentage: number;
}
