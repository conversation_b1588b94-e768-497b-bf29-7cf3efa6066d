"use client";

import * as React from "react";
import { Check, ChevronsUpDown, GalleryVerticalEnd } from "lucide-react";
import Cookies from "js-cookie";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar";
import { getStores } from "@/lib/api/retailStores/service";
import { useNavigate } from "react-router-dom";

// Function to abbreviate store names
const abbreviateStoreName = (storeName?: string | null): string => {
  if (!storeName) return "";

  // Normalize whitespace and remove punctuation we don't want in initials
  const cleaned = storeName
    .trim()
    .replace(/[\s\u00A0]+/g, " ") // collapse all whitespace
    .replace(/["'.,:/\\()\[\]{}<>!?@#$%^&*_+=`~]/g, "");

  // If the name is a single word, take up to first two letters (e.g., "Walmart" -> "WA")
  const words = cleaned.split(" ").filter(Boolean);
  if (words.length === 1) {
    return words[0].slice(0, 2).toUpperCase();
  }

  // Take the first letter of up to the first three words (avoid very long abbreviations)
  return words
    .slice(0, 3)
    .map((w) => w.charAt(0).toUpperCase())
    .join("");
};

export function StoreSwitcher({
  versions,
  defaultVersion,
}: {
  versions: string[];
  defaultVersion: string;
}) {
  const [selectedStore, setSelectedStore] = React.useState<string | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);
  const router = useNavigate();

  React.useEffect(() => {
    const initializeStore = async () => {
      const activeStoreId = Cookies.get("active_store");
      const token = Cookies.get("auth_token");
      
      if (activeStoreId && token) {
        try {
          const stores = await getStores(token);
          const storeName = stores.find((s) => s.id === Number(activeStoreId))?.name;
          setSelectedStore(storeName || defaultVersion);
        } catch (error) {
          console.error("Error fetching stores:", error);
          setSelectedStore(defaultVersion);
        }
      } else {
        setSelectedStore(defaultVersion);
      }
      setIsLoading(false);
    };
    
    initializeStore();
  }, [defaultVersion]);

  const handleStoreSelect = async (store: string) => {
    setSelectedStore(store);
    const token = Cookies.get("auth_token")!.toString();
    const stores = await getStores(token);

    const selectedStoreId = stores.find((s) => s.name === store)?.id;
    if (selectedStoreId) {
      Cookies.set("active_store", selectedStoreId.toString());
      Cookies.set("active_store_currency", stores.find((s) => s.id === selectedStoreId)?.currency || "USD");
      Cookies.set("active_store_url", stores.find((s) => s.id === selectedStoreId)?.url || "");
      router(0);
    }
  };

  const displayText = isLoading ? "..." : abbreviateStoreName(selectedStore);

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <SidebarMenuButton
              size="lg"
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <GalleryVerticalEnd className="size-4" />
              </div>
              <div className="flex flex-col gap-0.5 leading-none">
                <span className="font-semibold">StoreYako</span>
                <span className="">{displayText}</span>
              </div>
              <ChevronsUpDown className="ml-auto" />
            </SidebarMenuButton>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[--radix-dropdown-menu-trigger-width]"
            align="start"
          >
            {versions.map((version) => (
              <DropdownMenuItem
                key={version}
                onSelect={() => handleStoreSelect(version)}
              >
                {version}{" "}
                {version === selectedStore && <Check className="ml-auto" />}
              </DropdownMenuItem>
            ))}
          </DropdownMenuContent>
        </DropdownMenu>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}