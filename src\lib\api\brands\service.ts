import { BASE_URL } from "@/routes/configs/constants";
import type { Brand, BrandSchema } from "./models";

export async function fetchBrands(
  authtoken: string,
  storeId: number,
): Promise<Brand[]> {
  const url = `${BASE_URL}/brands/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authtoken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch brands: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching brands:", error);
    throw error;
  }
}

export async function fetchBrand(
  authtoken: string,
  storeId: number,
  brandId: number,
): Promise<Brand> {
  const url = `${BASE_URL}/brands/${storeId}/${brandId}`;
  const headers = {
    Authorization: `Bearer ${authtoken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch brand: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching brand:", error);
    throw error;
  }
}

export async function createBrand(
  authtoken: string,
  storeId: number,
  brand: BrandSchema,
): Promise<Brand> {
  const url = `${BASE_URL}/brands/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authtoken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(brand),
    });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create brand: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error creating brand:", error);
    throw error;
  }
}

export async function updateBrand(
  authtoken: string,
  storeId: number,
  brand: Brand,
): Promise<Brand> {
  const url = `${BASE_URL}/brands/${storeId}/${brand.id}`;
  const headers = {
    Authorization: `Bearer ${authtoken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(brand),
    });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to update brand: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error updating brand:", error);
    throw error;
  }
}

export async function deleteBrand(
  authtoken: string,
  storeId: number,
  brandId: number,
): Promise<void> {
  const url = `${BASE_URL}/brands/${storeId}/${brandId}`;
  const headers = {
    Authorization: `Bearer ${authtoken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to delete brand: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error deleting brand:", error);
    throw error;
  }
}
