"use client"

import { toast as sonnerToast } from "sonner"

// We map Sonner's functionality to match your existing hook's API
// so you don't have to change your component logic.
export function useToast() {
  return {
    toast: ({ title, description, action, ...props }: any) => {
      return sonnerToast(title, {
        description: description,
        action: action ? {
          label: action.props.children,
          onClick: action.props.onClick,
        } : undefined,
        ...props,
      })
    },
    dismiss: (toastId?: string | number) => sonnerToast.dismiss(toastId),
  }
}

// Export the direct toast function for use outside of components
export const toast = sonnerToast