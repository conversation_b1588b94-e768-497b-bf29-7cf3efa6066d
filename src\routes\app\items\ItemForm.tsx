"use client";

import React, { use<PERSON><PERSON>back, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupButton,
  InputGroupInput,
} from "@/components/ui/input-group";
import { Switch } from "@/components/ui/switch";
import type { Item, ItemSchema } from "@/lib/api/items/models";
import type { Brand } from "@/lib/api/brands/models";
import type { Category } from "@/lib/api/categories/models";
import type { Supplier } from "@/lib/api/suppliers/models";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import Cookies from "js-cookie";
import { uploadFileWithSignedUrl, validateFile } from "@/lib/firebase/storage";
import { useNavigate } from "react-router";
import { Scan, Loader2, Flashlight, ChevronDownIcon } from "lucide-react";
import { lazy, Suspense } from "react";
import { toast } from "sonner";

const LazyBarcodeScanner = lazy(() => import("react-qr-barcode-scanner"));

const BarcodeScanner = (props: any) => (
  <Suspense fallback={<div>Loading...</div>}>
    <LazyBarcodeScanner {...props} />
  </Suspense>
);

interface ItemFormProps {
  initialData?: ItemSchema;
  onSubmit(data: ItemSchema[] | Item[]): Promise<void>;
  isEditing?: boolean;
  categories: Category[];
  suppliers: Supplier[];
  brands: Brand[];
}

export function ItemForm({
  initialData,
  onSubmit,
  isEditing = false,
  categories,
  suppliers,
  brands,
}: ItemFormProps) {
  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
  } = useForm<ItemSchema>({
    defaultValues: initialData || {
      name: "",
      quantity: 0,
      default_cost: 0,
      default_price: 0,
      brand: 0,
      has_discount: false,
      tags: [],
      is_variant: false,
      category: 0,
      store_id: 0,
      vendor_id: 0,
      image: "",
      description: "",
      sku: "",
      barcode: "",
      notes: "",
      date_created: "",
      //parent_item_id: null,
      vat_included: false,
      vat_percentage: 0,
    },
  });

  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [uploading, setUploading] = useState<boolean>(false);
  const router = useNavigate();

  const [isScannerOpen, setIsScannerOpen] = useState(false);
  const [isScanning, setIsScanning] = useState(false);
  const [cameraReady, setCameraReady] = useState(false);
  const [cameraError, setCameraError] = useState<string>("");
  const [torchEnabled, setTorchEnabled] = useState<boolean>(false);

  useEffect(() => {
    if (initialData) {
      reset(initialData);
    }
  }, [initialData, reset]);

  const handleBarcodeScanned = useCallback((barcode: string) => {
    if (isScanning) return;

    setIsScanning(true);
    console.log("Scanned barcode:", barcode);

    setValue("barcode", barcode);
    toast.success(`Barcode scanned: ${barcode}`);

    setTimeout(() => {
      setIsScannerOpen(false);
      setIsScanning(false);
      setCameraReady(false);
      setCameraError("");
      setTorchEnabled(false);
    }, 1000);
  }, [isScanning, setValue]);

  const openBarcodeScanner = useCallback(() => {
    setIsScannerOpen(true);
    setCameraError("");
    setCameraReady(false);
  }, []);

  const closeBarcodeScanner = useCallback(() => {
    setIsScannerOpen(false);
    setIsScanning(false);
    setCameraReady(false);
    setCameraError("");
    setTorchEnabled(false);
  }, []);

  const handleImageChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        setValue("image", file);
      }
    },
    [setValue],
  );

  const handleFormSubmit = async (data: ItemSchema) => {
    setError(null);
    setSuccess(false);
    setUploading(true);

    try {
      if (data.image instanceof File) {
        const validation = validateFile(data.image);
        if (!validation.isValid) {
          throw new Error(validation.error);
        }

        toast.info("Uploading image to Firebase...");
        const folder = "products";
        const storeUrl = Cookies.get("active_store_url") || "";
        const token = Cookies.get("auth_token") || "";
        if (!storeUrl) {
          throw new Error("Store URL is required for image upload");
        }
        if (!token) {
          throw new Error("Authentication token is required for image upload");
        }
        const imageUrl = await uploadFileWithSignedUrl(data.image, folder, storeUrl, token);
        data.image = imageUrl;
        toast.success("Image uploaded successfully!");
      }

      await onSubmit([data]);
      setSuccess(true);
      router("/app/items");
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-5xl">
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Card>
          {error && <AlertError message={error} />}
          {success && <AlertSuccess message={`Product ${isEditing ? 'updated' : 'added'} successfully`} />}
          <CardHeader>
            <CardTitle>{isEditing ? 'Edit Product' : 'Add Product'}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="required">
                    Product Name
                  </Label>
                  <Input id="name" {...register("name", { required: true })} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category</Label>
                  <InputGroup>
                    <InputGroupInput
                      placeholder="Select Category"
                      value={categories.find(c => c.id === watch("category"))?.name || ''}
                      readOnly
                    />
                    <InputGroupAddon align="inline-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                            Select <ChevronDownIcon className="size-3" />
                          </InputGroupButton>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {categories.map((category) => (
                            <DropdownMenuItem
                              key={category.id}
                              onClick={() => setValue("category", category.id)}
                            >
                              {category.name}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </InputGroupAddon>
                  </InputGroup>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="brand">Brand/Model</Label>
                  <InputGroup>
                    <InputGroupInput
                      placeholder="Select Brand"
                      value={brands.find(b => b.id === watch("brand"))?.name || ''}
                      readOnly
                    />
                    <InputGroupAddon align="inline-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                            Select <ChevronDownIcon className="size-3" />
                          </InputGroupButton>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          {brands.map((brand) => (
                            <DropdownMenuItem
                              key={brand.id}
                              onClick={() => setValue("brand", brand.id)}
                            >
                              {brand.name}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </InputGroupAddon>
                  </InputGroup>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="image">Image</Label>
                  <Input
                    id="image"
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Detail</Label>
                  <Textarea
                    id="description"
                    {...register("description")}
                    placeholder="Product details..."
                    className="h-32"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity" className="required">
                  Quantity
                </Label>
                <Input
                  id="quantity"
                  type="number"
                  step="1"
                  {...register("quantity", { required: true, min: 0, valueAsNumber: true })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="default_price" className="required">
                  Sell Price
                </Label>
                <Input
                  id="default_price"
                  type="number"
                  step="0.01"
                  {...register("default_price", { required: true, min: 0, valueAsNumber: true })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="default_cost" className="required">
                  Supplier Price
                </Label>
                <Input
                  id="default_cost"
                  type="number"
                  step="0.01"
                  {...register("default_cost", { required: true, min: 0, valueAsNumber: true })}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku" className="required">
                  SKU
                </Label>
                <Input id="sku" {...register("sku", { required: true })} />
              </div>

              <div className="space-y-2">
                <Label htmlFor="vendor">Supplier</Label>
                <InputGroup>
                  <InputGroupInput
                    placeholder="Select supplier"
                    value={suppliers.find(s => s.id === watch("vendor_id"))?.name || ''}
                    readOnly
                  />
                  <InputGroupAddon align="inline-end">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                          Select <ChevronDownIcon className="size-3" />
                        </InputGroupButton>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {suppliers.map((supplier) => (
                          <DropdownMenuItem
                            key={supplier.id}
                            onClick={() => setValue("vendor_id", supplier.id)}
                          >
                            {supplier.name}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </InputGroupAddon>
                </InputGroup>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="tags">Tags (semicolon-separated)</Label>
              <Input
                id="tags"
                value={watch("tags")?.join("; ") || ""}
                onChange={(e) => {
                  const tagsArray = e.target.value
                    .split(";")
                    .map((tag) => tag.trim())
                    .filter((tag) => tag.length > 0);
                  setValue("tags", tagsArray);
                }}
                placeholder="e.g., tag1; tag2; tag3"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="barcode">Barcode</Label>
              <div className="flex gap-2">
                <Input
                  id="barcode"
                  {...register("barcode")}
                  placeholder="Enter barcode or scan"
                  className="flex-1"
                />
                <Button
                  type="button"
                  onClick={openBarcodeScanner}
                  variant="outline"
                  size="icon"
                  className="shrink-0"
                >
                  <Scan className="h-4 w-4" />
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 items-start gap-y-6 rounded-lg border p-4 md:grid-cols-2 md:gap-x-8">

              {/* Discount Section */}
              {/* Discount Section */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="has_discount"
                    checked={watch("has_discount")}
                    onCheckedChange={(checked) => {
                      setValue("has_discount", checked);
                      if (!checked) {
                        setValue("discount", 0); // Reset percentage if disabled
                      }
                    }}
                  />
                  <Label htmlFor="has_discount" className="font-medium cursor-pointer">
                    Enable Discount
                  </Label>
                </div>

                {watch("has_discount") && (() => {
                  const defaultPrice = watch("default_price") || 0;
                  const discountPercentage = watch("discount") || 0;
                  const calculatedDeduction = (defaultPrice * discountPercentage) / 100;

                  const formattedDeduction = new Intl.NumberFormat('en-KE', {
                    style: 'currency',
                    currency: 'KES',
                  }).format(calculatedDeduction);

                  return (
                    <div className="grid w-full max-w-sm items-center gap-2 pl-10">
                      <Label htmlFor="discount">Discount Percentage (%)</Label>
                      <Input
                        id="discount"
                        type="number"
                        step="1"
                        placeholder="e.g., 10"
                        min="0"
                        max="100"
                        {...register("discount", { valueAsNumber: true, min: 0, max: 100 })}
                      />
                      {calculatedDeduction > 0 && (
                        <p className="text-sm text-muted-foreground">
                          Deducted Amount: <span className="font-semibold text-foreground">{formattedDeduction}</span>
                        </p>
                      )}
                    </div>
                  );
                })()}
              </div>
              {/* VAT Section */}
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Switch
                    id="vat_included"
                    checked={watch("vat_included")}
                    onCheckedChange={(checked) => setValue("vat_included", checked)}
                  />
                  <Label htmlFor="vat_included" className="font-medium cursor-pointer">
                    VAT Included
                  </Label>
                </div>
                {watch("vat_included") && (
                  <div className="grid w-full max-w-sm items-center gap-1.5 pl-10">
                    <Label htmlFor="vat_percentage">VAT Percentage (%)</Label>
                    <Input
                      id="vat_percentage"
                      type="number"
                      step="0.01"
                      min="0"
                      max="100"
                      placeholder="16"
                      {...register("vat_percentage", { valueAsNumber: true, min: 0, max: 100 })}
                    />
                  </div>
                )}
              </div>

            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                {...register("notes")}
                placeholder="Additional notes..."
              />
            </div>

            <Button type="submit" className="w-full" disabled={uploading}>
              {uploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Please wait
                </>
              ) : isEditing ? 'Update Product' : 'Add Product'}
            </Button>
          </CardContent>
        </Card>
      </form>

      <Dialog open={isScannerOpen} onOpenChange={setIsScannerOpen}>
        <DialogContent className="barcode-scanner-dialog w-[95vw] h-[85vh] max-w-sm p-0 overflow-hidden flex flex-col sm:w-[90vw] sm:h-[75vh] sm:max-w-md">
          <DialogHeader className="flex-shrink-0 p-3 sm:p-4 border-b bg-background">
            <DialogTitle className="flex items-center text-base sm:text-lg">
              <Scan className="mr-2 h-5 w-5 flex-shrink-0" />
              <span className="truncate">Scan Barcode</span>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 flex flex-col items-center justify-center p-2 sm:p-4 bg-background min-h-0 overflow-hidden">
            {cameraError ? (
              <div className="text-center text-foreground px-4 max-w-sm mx-auto">
                <div className="mb-6">
                  <Scan className="h-20 w-20 sm:h-16 sm:w-16 mx-auto text-muted-foreground" />
                </div>
                <h3 className="text-xl sm:text-lg font-semibold mb-4 sm:mb-2">Camera Access Required</h3>
                <p className="text-base sm:text-sm text-muted-foreground mb-6 sm:mb-4 leading-relaxed">{cameraError}</p>
                <div className="space-y-4 sm:space-y-2">
                  <Button
                    onClick={async () => {
                      try {
                        const stream = await navigator.mediaDevices.getUserMedia({
                          video: { facingMode: 'environment' }
                        });
                        stream.getTracks().forEach(track => track.stop());
                        setCameraError("");
                        setCameraReady(false);
                        toast.success("Camera access granted!");
                      } catch (error: any) {
                        setCameraError("Camera permission denied. Please allow camera access.");
                        toast.error("Camera permission denied");
                      }
                    }}
                    className="w-full h-12 sm:h-10 text-base sm:text-sm"
                  >
                    Allow Camera Access
                  </Button>
                </div>
              </div>
            ) : (
              <div className="relative w-full h-full max-w-sm mx-auto flex-1 min-h-0">
                <BarcodeScanner
                  width="100%"
                  height="100%"
                  facingMode="environment"
                  delay={300}
                  torch={torchEnabled}
                  onUpdate={(err: any, result: any) => {
                    if (err) {
                      if ((err as any)?.name === "NotFoundException" ||
                        (err as any)?.message?.includes("No MultiFormat Readers were able to detect the code")) {
                        if (!cameraReady) {
                          setCameraReady(true);
                        }
                        return;
                      }
                      setCameraReady(true);
                      setCameraError("Camera error. Please check permissions and try again.");
                      return;
                    }

                    if (!cameraReady) {
                      setCameraReady(true);
                    }

                    if (result && !isScanning) {
                      let scannedText = '';
                      try {
                        if (typeof result === 'string') {
                          scannedText = result;
                        } else if (result && typeof result === 'object') {
                          scannedText = (result as any).getText?.() ||
                            (result as any).text ||
                            (result as any).data ||
                            String(result);
                        }

                        if (scannedText && typeof scannedText === 'string') {
                          handleBarcodeScanned(scannedText);
                        }
                      } catch (error) {
                        console.warn('Error processing barcode result:', error);
                      }
                    }
                  }}
                />

                {!cameraReady && (
                  <div className="absolute inset-0 bg-background/75 flex items-center justify-center">
                    <div className="text-center text-foreground">
                      <Loader2 className="h-10 w-10 sm:h-8 sm:w-8 animate-spin mx-auto mb-3 sm:mb-2 text-primary" />
                      <p className="text-base sm:text-sm">Initializing camera...</p>
                    </div>
                  </div>
                )}

                {cameraReady && (
                  <div className="absolute inset-0 pointer-events-none">
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-48 h-48 sm:w-56 sm:h-56 border-2 border-border rounded-lg">
                      <div className="absolute top-0 left-0 w-10 h-10 sm:w-8 sm:h-8 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
                      <div className="absolute top-0 right-0 w-10 h-10 sm:w-8 sm:h-8 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
                      <div className="absolute bottom-0 left-0 w-10 h-10 sm:w-8 sm:h-8 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
                      <div className="absolute bottom-0 right-0 w-10 h-10 sm:w-8 sm:h-8 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
                    </div>
                  </div>
                )}

                {cameraReady && (
                  <div className="absolute top-4 right-4 pointer-events-auto">
                    <Button
                      onClick={() => setTorchEnabled(!torchEnabled)}
                      variant={torchEnabled ? "default" : "secondary"}
                      size="icon"
                      className="h-12 w-12 rounded-full"
                    >
                      <Flashlight className="h-5 w-5" />
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}