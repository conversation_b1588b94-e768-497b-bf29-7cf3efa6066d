// export function formatCurrency(amount: number, currency: string = 'KES'): string {
//   return new Intl.NumberFormat('en-US', {
//     style: 'currency',
//     currency: currency,
//     minimumFractionDigits: 2,
//     maximumFractionDigits: 2
//   }).format(amount);
// }


import Cookies from "js-cookie";

/**
 * Returns an Intl.NumberFormat configured for the store's active currency (if valid).
 */
function getCurrencyFormatter() {
  const storeCurrency = Cookies.get("active_store_currency");

  if (!storeCurrency) return null;

  // ISO-style currency like USD, EUR, KES
  if (/^[A-Z]{3}$/.test(storeCurrency)) {
    try {
      return new Intl.NumberFormat(navigator.language || undefined, {
        style: "currency",
        currency: storeCurrency,
        maximumFractionDigits: 0,
      });
    } catch {
      return null;
    }
  }

  // if not an ISO code (e.g. "$"), skip formatter
  return null;
}

/**
 * Formats a number into a currency string using store's active currency.
 * Fallback: uses store_currency as symbol if not ISO code.
 */
export function formatCurrency(value: number): string {
  const storeCurrency = Cookies.get("active_store_currency") ?? "";
  const formatter = getCurrencyFormatter();

  if (formatter) return formatter.format(value);

  // fallback: symbol + number
  return `${storeCurrency} ${value.toLocaleString()}`;
}
