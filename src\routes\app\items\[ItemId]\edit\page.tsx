"use client";

import { useEffect, useState } from "react";
import { Link, useParams } from "react-router";;
import Cookies from "js-cookie";
import { ItemForm } from "@/routes/app/items/ItemForm"; // Assuming ItemForm handles its own internal states/feedback
import type { Category } from "@/lib/api/categories/models";
import type { Supplier } from "@/lib/api/suppliers/models";
import type { Brand } from "@/lib/api/brands/models";
import { fetchCategories } from "@/lib/api/categories/service";
import { fetchSuppliers } from "@/lib/api/suppliers/service";
import { fetchBrands } from "@/lib/api/brands/service";
import { fetchItem, updateItem } from "@/lib/api/items/service";
import type { ItemSchema } from "@/lib/api/items/models";
import { Button } from "@/components/ui/button";
// import { inter } from "@/app/ui/fonts";


export default function EditItemPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [brands, setBrands] = useState<Brand[]>([]);
  const [initialData, setInitialData] = useState<ItemSchema | null>(null);
  const [itemName, setItemName] = useState<string>(""); // Store item name for breadcrumb
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const [isNotFound, setIsNotFound] = useState<boolean>(false); // Specific state for 404 type errors

  const token = Cookies.get("auth_token");
  const store_id = Number(Cookies.get("active_store"));
  const itemId = Number(useParams().ItemId);

  useEffect(() => {
    if (!itemId) {
      setError(new Error("Item ID is missing from the URL."));
      setIsLoading(false);
      return;
    }

    if (!token || !store_id) {
      setError(new Error("Authentication token or store ID is missing. Please log in again."));
      setIsLoading(false);
      return;
    }

    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      setIsNotFound(false);

      try {
        // Fetch dropdown data and item data concurrently
        const [categoriesData, suppliersData, brandsData, item] = await Promise.all([
          fetchCategories(token, store_id),
          fetchSuppliers(token, store_id),
          fetchBrands(token, store_id),
          fetchItem(token, store_id, itemId), // Fetch the specific item
        ]);

        // Set dropdown data
        setCategories(categoriesData);
        setSuppliers(suppliersData);
        setBrands(brandsData);

        // Process and set item data for the form
        //const tagsArray = item.tags ? Object.keys(item.tags) : [];

        //console.log(tags);
        const itemSchema: ItemSchema = {
          name: item.name,
          quantity: item.quantity,
          default_cost: item.default_cost,
          default_price: item.default_price,
          brand: item.brand,
          has_discount: item.has_discount,
          date_created: item.date_created, // Keep if needed by form, otherwise can omit
          tags: item.tags,
          category: item.category,
          store_id: store_id,
          vendor_id: item.vendor_id,
          image: item.image || "",
          description: item.description || "",
          sku: item.sku || "",
          barcode: item.barcode || "",
          discount: item.discount || undefined,
          notes: item.notes || "",
          is_variant: item.is_variant,
          parent_item_id: item.parent_item_id,
          vat_included: item.vat_included ?? false,
          vat_percentage: item.vat_percentage ?? 0,
        };

        setInitialData(itemSchema);
        setItemName(item.name); // Set item name for breadcrumb

      } catch (err: any) {
        // Differentiate "Not Found" from other errors if possible
        // This depends on how your `fetchItem` API call signals a 404
        // Example: if (err.response?.status === 404) {
        //   setIsNotFound(true);
        // } else {
        setError(err instanceof Error ? err : new Error("Failed to fetch required data for editing."));
        // }
        // For now, treat all fetch errors as general errors, ItemForm might load empty
        console.error("Failed to fetch data:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [token, store_id, itemId]); // Use dependency array correctly

  const handleSubmit = async (data: ItemSchema[]) => {
    // Validation and error handling likely happen within ItemForm's onSubmit process
    // This function just passes the update call
    if (!token || !store_id || !itemId) {
      // This error should ideally be caught and displayed by ItemForm
      throw new Error("Cannot submit: Authentication token, store ID, or item ID is missing.");
    }

    // Assuming updateItem throws an error on failure, which ItemForm should handle
    await updateItem(token, store_id, itemId, data[0]);
    // Optional: Add success feedback/redirect here if ItemForm doesn't handle it
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading item details for editing...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error Loading Item Data</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/items">
              <Button variant="link">Return to Items List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Handle case where item wasn't found (requires specific check in catch block)
  if (isNotFound) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Item Not Found</h2>
          <p className="text-gray-600 text-center">The item with ID ({itemId}) could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/items">
              <Button variant="link">Return to Items List</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // If initialData is still null after loading without error/not found, show generic message
  if (!initialData) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <p className="text-gray-600">Item data could not be prepared for editing.</p>
        <Link to="/app/items" className="ml-4">
          <Button variant="link">Return to Items List</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className="bg-gray-50 min-h-screen">
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500 items-center">
            <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link to="/app/items" className="hover:text-blue-600">Items</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Edit: {itemName || `Item ${itemId}`}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6 pb-6">
        {/* You might want a title here */}
        {/* <h1 className="text-2xl font-bold text-gray-800 mb-4">Edit Item</h1> */}
        {/* ItemForm likely contains its own Card or structure */}
        <ItemForm
          initialData={initialData}
          onSubmit={handleSubmit}
          categories={categories}
          suppliers={suppliers}
          brands={brands}
          isEditing={true}
        />
      </div>
    </div>
  );
}