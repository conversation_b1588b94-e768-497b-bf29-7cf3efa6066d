// Legacy interfaces (keeping for backward compatibility)
export interface Permission {
  id: number;
  name: string;
  description?: string;
}

export interface RoleWithPermissions {
  id: number;
  name: string;
  description?: string;
  permissions: Permission[];
}

// New interfaces for the enhanced users endpoint
export interface UserGroupInfo {
  group_id: number;
  group_name: string;
  group_description?: string;
  permissions: string[];
}

export interface UserWithRoleDetails {
  user_id: number;
  name: string;
  email: string;
  store_id: number;
  store_name: string;
  user_defined_role: string;
  groups: UserGroupInfo[];
  all_permissions: string[];
}

export interface UsersWithRolesResponse {
  store_id: string;
  total_users: number;
  users: UserWithRoleDetails[];
}

// Permission categories for better organization
export interface PermissionCategory {
  name: string;
  permissions: string[];
  icon: string;
  color: string;
}

// User role statistics
export interface RoleStats {
  role: string;
  count: number;
  percentage: number;
}