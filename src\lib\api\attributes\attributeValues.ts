import { BASE_URL } from "@/routes/configs/constants";
import type { AttributeValues, AttributeValuesSchema } from "./models";


export async function createAttributesValues(
  authToken: string,
  storeId: number,
  attributeId: number,
  payload: AttributeValuesSchema
): Promise<void> {
  const url = `${BASE_URL}/items/attributes/values/${storeId}/${attributeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });
    if (response.status !== 201) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create attribute value: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error creating attribute value:", error);
    throw error;
  }
}


export async function fetchAttributeValues(
  authToken: string,
  storeId: number,
  attributeId: number
): Promise<AttributeValues[]> {
  const url = `${BASE_URL}/items/attributes/values/${storeId}/${attributeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch attribute values: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching attribute values:", error);
    throw error;
  }

}

export async function fetchStoreAttributeValues(
  authToken: string,
  storeId: number,
): Promise<AttributeValues[]> {
  const url = `${BASE_URL}/items/attributes/values/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch attribute values: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching attribute values:", error);
    throw error;
  }
}
