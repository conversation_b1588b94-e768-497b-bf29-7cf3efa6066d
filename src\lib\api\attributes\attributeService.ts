import { BASE_URL } from "@/routes/configs/constants";
import type { Attributes, AttributePayload } from "@/lib/api/attributes/models";

export async function fetchAttributes(
  authToken: string,
  storeId: number
): Promise<Attributes[]> {
  const url = `${BASE_URL}/items/attributes/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to fetch attributes: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error fetching attributes:", error);
    throw error;
  }

}

export async function createAttribute(
  authToken: string,
  storeId: number,
  payload: AttributePayload
): Promise<Attributes> {
  const url = `${BASE_URL}/items/attributes/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to create attribute: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error creating attribute:", error);
    throw error;
  }
}

export async function deleteAttribute(
  authToken: string,
  storeId: number,
  attributeId: number
): Promise<void> {
  const url = `${BASE_URL}/items/attributes/${storeId}/${attributeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers,
    });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to delete attribute: ${errorMessage.error}`);
    }
  } catch (error) {
    console.error("Error deleting attribute:", error);
    throw error;
  }
}

export async function updateAttribute(
  authToken: string,
  storeId: number,
  attributeId: number,
  payload: AttributePayload
): Promise<Attributes> {
  const url = `${BASE_URL}/items/attributes/${storeId}/${attributeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers,
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(`Failed to update attribute: ${errorMessage.error}`);
    }
    return await response.json();
  } catch (error) {
    console.error("Error updating attribute:", error);
    throw error;
  }
}
