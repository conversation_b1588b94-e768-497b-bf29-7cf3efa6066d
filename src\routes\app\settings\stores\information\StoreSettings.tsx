"use client";

import { useEffect, useState } from "react";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { getStore } from "@/lib/api/retailStores/service";
import Cookies from "js-cookie";
import { Label } from "@/components/ui/label";
import type { Store } from "@/lib/api/retailStores/models";
import { useNavigate } from "react-router";
import { Link } from "react-router";;

function LoadingSpinner() {
    return (
        <div className="min-h-screen flex justify-center items-center bg-gray-50">
            <div className="animate-pulse flex flex-col items-center">
                <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
                <p className="mt-4 text-gray-600 font-medium">Loading store details...</p>
            </div>
        </div>
    );
}

export default function StoreSettings() {
    const [store, setStore] = useState<Store | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const router = useNavigate();

    useEffect(() => {
        const fetchStoreData = async () => {
            try {
                const token = Cookies.get("auth_token");
                const storeId = Number(Cookies.get("active_store"));

                if (!token || !storeId) {
                    throw new Error("Missing authentication token or store ID.");
                }

                const storeData = await getStore(storeId, token);
                setStore(storeData);
            } catch (err) {
                setError(err instanceof Error ? err.message : "Failed to fetch store data.");
            } finally {
                setLoading(false);
            }
        };

        fetchStoreData();
    }, []);

    if (loading) {
        return (
            <div className="min-h-screen flex justify-center items-center bg-gray-50">
                <LoadingSpinner />
            </div>
        );
    }

    if (error) {
        return (
            <div className="min-h-screen flex justify-center items-center bg-gray-50">
                <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
                    <div className="text-red-500 flex items-center justify-center mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    <h2 className="text-xl font-bold text-center mb-2">Error</h2>
                    <p className="text-gray-600 text-center">{error}</p>
                    <div className="mt-6 flex justify-center">
                        <Link to="/app/dashboard">
                            <Button variant="link">Return to Dashboard</Button>
                        </Link>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-gray-50 min-h-screen">
            {/* Breadcrumb navigation */}
            <div className="bg-white shadow-sm">
                <div className="container mx-auto px-4 py-3">
                    <div className="flex text-sm text-gray-500">
                        <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
                        <span className="mx-2">/</span>
                        <span className="text-gray-700 font-medium">Store Information</span>
                    </div>
                </div>
            </div>

            {/* Content */}
            <div className="flex justify-center items-start min-h-[calc(100vh-100px)] bg-gray-100 p-4">
                <Card className="w-full max-w-2xl shadow-lg">
                    <CardHeader className="text-center">
                        <img
                            src={store?.image_url || `https://ui-avatars.com/api/?name=${store?.name}&background=ccffcc&color=fff&format=svg&size=128&bold=true`}
                            alt={store?.name || "Store Logo"}
                            className="w-32 h-32 rounded-full object-cover mx-auto shadow-sm"
                        />

                        <CardTitle className="mt-4 text-2xl font-bold">{store?.name || "Unnamed Store"}</CardTitle>
                        <p className="text-sm text-gray-500">Store Settings</p>
                    </CardHeader>

                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {[
                                { label: "Store Email", value: store?.email },
                                { label: "Phone Number", value: store?.phone_number },
                                { label: "Opening Hours", value: store?.opening_hours },
                                { label: "Payment Methods", value: store?.payment_methods },
                                { label: "Description", value: store?.notes },
                                { label: "Tax Rate", value: store?.tax_rate ? `${store.tax_rate}%` : null },
                                { label: "Currency", value: store?.currency },
                            ].map(({ label, value }) => (
                                <div key={label}>
                                    <Label className="font-semibold text-gray-700">{label}</Label>
                                    <p className="text-gray-900 text-sm">{value || "Not provided"}</p>
                                </div>
                            ))}
                        </div>

                        <div className="flex justify-end">
                            <Button className="px-6 py-2 text-white"
                                onClick={() => router(`/app/stores/${store?.id}/edit`)}
                            >
                                Edit Store
                            </Button>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
}