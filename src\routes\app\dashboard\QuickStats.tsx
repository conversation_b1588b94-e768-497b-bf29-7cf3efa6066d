import type { DashboardStats } from '@/lib/api/reports/models';

interface QuickStatsProps {
    stats: DashboardStats | null;
}

const QuickStats = ({ stats }: QuickStatsProps) => {
    if (!stats) {
        return <div className="col-span-1 md:col-span-2 lg:col-span-12">No dashboard stats available.</div>;
    }

    return (
        <>
            <div className="col-span-1 md:col-span-1 lg:col-span-3 rounded-lg border bg-card p-4 shadow-sm">
                <div className={"flex justify-between items-center"}>
                    <h3 className="text-sm font-medium text-gray-500">Total Revenue</h3>
                    <div className={"rounded-full bg-gray-500 h-6 w-6 flex items-center justify-center"}>
                        <span className={"text-white text-sm"}>$</span>
                    </div>
                </div>
                <p className={"text-lg font-semibold"}>${stats.total_revenue?.toFixed(2) || '0.00'}</p>
                <p className={`text-xs ${stats.total_revenue_change > 0 ? "text-green-500" : "text-red-500"}`}>
                    {stats.total_revenue_change > 0 ? "+" : ""}{stats.total_revenue_change?.toFixed(2) || '0.00'}% from last month
                </p>

            </div>
            <div className="col-span-1 md:col-span-1 lg:col-span-3 rounded-lg border bg-card p-4 shadow-sm">
                <div className={"flex justify-between items-center"}>
                    <h3 className="text-sm font-medium text-gray-500">Total Transactions</h3>
                    <div className={"rounded-full bg-gray-500 h-6 w-6 flex items-center justify-center"}>
                        <span className={"text-white text-sm"}>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                                stroke="currentColor" className="w-4 h-4">
                                <path strokeLinecap="round" strokeLinejoin="round"
                                    d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                            </svg>
                        </span>
                    </div>
                </div>
                <p className={"text-lg font-semibold"}>+{stats.total_transactions}</p>
                <p className={`text-xs ${stats.total_transactions_change > 0 ? "text-green-500" : "text-red-500"}`}>
                    {stats.total_transactions_change > 0 ? "+" : ""}{stats.total_transactions_change?.toFixed(2) || '0.00'}% from last month
                </p>

            </div>
            <div className="col-span-1 md:col-span-1 lg:col-span-3 rounded-lg border bg-card p-4 shadow-sm">

                <div className={"flex justify-between items-center"}>
                    <h3 className="text-sm font-medium text-gray-500">Items Sold</h3>
                    <div className={"rounded-full bg-gray-500 h-6 w-6 flex items-center justify-center"}>
                        <span className={"text-white text-sm"}>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                                stroke="currentColor" className="w-4 h-4">
                                <path strokeLinecap="round" strokeLinejoin="round"
                                    d="M2.25 18.75a60.07 60.07 0 0 1 15.797 2.101c.727.198 1.453-.342 1.453-1.096V18.75M3.75 4.5v.75A.75.75 0 0 1 3 6h-.75m0 0v-.375c0-.621.504-1.125 1.125-1.125H20.25M2.25 6v9m18-10.5v.75c0 .414.336.75.75.75h.75m-1.5-1.5h.375c.621 0 1.125.504 1.125 1.125v9.75c0 .621-.504 1.125-1.125 1.125h-.375m1.5-1.5H21a.75.75 0 0 0-.75.75v.75m0 0H3.75m0 0h-.375a1.125 1.125 0 0 1-1.125-1.125V15m1.5 1.5v-.75A.75.75 0 0 0 3 15h-.75M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm3 0h.008v.008H18V10.5Zm-12 0h.008v.008H6V10.5Z" />
                            </svg>
                        </span>
                    </div>
                </div>
                <p className={"text-lg font-semibold"}>+{stats.items_sold}</p>
                <p className={`text-xs ${stats.items_sold_change > 0 ? "text-green-500" : "text-red-500"}`}>
                    {stats.items_sold_change > 0 ? "+" : ""}{stats.items_sold_change?.toFixed(2) || '0.00'}% from last month
                </p>
            </div>
            <div className="col-span-1 md:col-span-1 lg:col-span-3 rounded-lg border bg-card p-4 shadow-sm">

                <div className={"flex justify-between items-center"}>
                    <h3 className="text-sm font-medium text-gray-500">Transactions Last Hour</h3>
                    <div className={"rounded-full bg-gray-500 h-6 w-6 flex items-center justify-center"}>
                        <span className={"text-white text-sm"}>
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5}
                                stroke="currentColor" className="w-4 h-4">
                                <path strokeLinecap="round" strokeLinejoin="round"
                                    d="M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z" />
                            </svg>
                        </span>
                    </div>
                </div>
                <p className={"text-lg font-semibold"}>+{stats.transactions_last_hour}</p>
                <p className={`text-xs ${stats.transactions_last_hour_change > 0 ? "text-green-500" : "text-red-500"}`}>
                    {stats.transactions_last_hour_change > 0 ? "+" : ""}{stats.transactions_last_hour_change?.toFixed(2) || '0.00'} since last hour
                </p>
            </div>

        </>
    );
};

export default QuickStats;