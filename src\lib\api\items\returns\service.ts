import { BASE_URL } from "@/routes/configs/constants";
import type { Returns, ReturnSchema } from "./models";

export async function fetchReturns(
  token: string,
  storeId: number,
): Promise<Returns[]> {
  const url = `${BASE_URL}/items/returns/${storeId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      throw new Error("Error fetching returns");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function createReturn(
  authToken: string,
  storeId: number,
  payload: ReturnSchema,
): Promise<Returns> {
  const url = `${BASE_URL}/items/returns/${storeId}`;

  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(payload),
    });
    if (response.status != 201) {
      throw new Error("Error creating return");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function getReturn(
  authToken: string,
  storeId: number,
  return_id: number,
): Promise<Returns> {
  const url = `${BASE_URL}/items/returns/${storeId}/${return_id}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      throw new Error("Error fetching return");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function updateReturn(
  authToken: string,
  storeId: number,
  return_id: number,
  payload: ReturnSchema,
): Promise<Returns> {
  const url = `${BASE_URL}/items/returns/${storeId}/${return_id}`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(payload),
    });
    if (response.status != 200) {
      throw new Error("Error updating return");
    }
    const data = await response.json();
    return data;
  } catch (error) {
    throw error;
  }
}

export async function deleteReturn(
  authToken: string,
  storeId: number,
  return_id: number,
): Promise<void> {
  const url = `${BASE_URL}/items/returns/${storeId}/${return_id}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (response.status != 204) {
      throw new Error("Error deleting return");
    }
  } catch (error) {
    throw error;
  }
}
