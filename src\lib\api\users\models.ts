export interface User {
  id: number;
  url: string;
  name: string;
  email: string;
  confirm: boolean;
  confirmedOn?: string;
  roleId: number;
  hasAccess: boolean;
  profile?: string;
  planId?: number;
  userDefinedRoleId?: number;
  createdOn?: string;
  updatedAt?: string;
  lastSeen?: string;
}


export interface UserInfoResponse {
  id: number;
  url: string;
  name: string;
  email: string;
  confirm: boolean;
  confirmed_on?: string;
  role: string;
  hasAccess: boolean;
  plan: string;
  user_defined_role?: string;
  created_on?: string;
  updated_at?: string;
  last_seen?: string;
}

export interface NewStaff {
  name: string,
  email: string,
  user_defined_role_id?: number,
  profile?: string,
}
