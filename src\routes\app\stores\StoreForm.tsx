"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupButton,
  InputGroupInput,
} from "@/components/ui/input-group";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import type { StoreSchema } from "@/lib/api/retailStores/models";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import { useNavigate } from "react-router";
import { uploadFileWithSignedUrl, validateFile } from "@/lib/firebase/storage";
import { getAllCountries } from '@/lib/api/users/onboarding/utils'; // Ensure this path is correct
import Cookies from "js-cookie";
import { toast } from "sonner";
import { Loader2, ChevronDown, Check, ChevronsUpDown, Search, Globe } from "lucide-react";

const storeFormSchema = z.object({
  name: z.string().min(1, "Store name is required"),
  currency: z.string().max(3, "Currency code must be 3 characters or less").optional(),
  street_address: z.string().max(100, "Street address must be 100 characters or less").optional(),
  city: z.string().max(50, "City must be 50 characters or less").optional(),
  state: z.string().max(50, "State must be 50 characters or less").optional(),
  postal_code: z.string().max(20, "Postal code must be 20 characters or less").optional(),
  address: z.string().max(100, "Address must be 100 characters or less").optional(),
  country: z.string().max(50, "Country must be 50 characters or less").optional(),
  phone_number: z.string().optional(),
  email: z.string().email("Invalid email format").optional().or(z.literal("")),
  opening_hours: z.string().max(100, "Opening hours must be 100 characters or less").optional(),
  category: z.string().max(100, "Category must be 100 characters or less").optional(),
  payment_methods: z.string().max(255, "Payment methods must be 255 characters or less").optional(),
  notes: z.string().max(1000, "Notes must be 1000 characters or less").optional(),
  status: z.string().max(20, "Status must be 20 characters or less").optional(),
  image_url: z.string().optional(),
  image: z.any().optional(),
});

type StoreFormData = z.infer<typeof storeFormSchema>;

interface StoreFormProps {
  initialData?: StoreSchema;
  onSubmit: (data: StoreSchema) => Promise<void>;
  isEditing?: boolean;
  storeUrl?: string;
}

export function StoreForm({
  initialData,
  onSubmit,
  isEditing = false,
  storeUrl,
}: StoreFormProps) {
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
  } = useForm<StoreFormData>({
    resolver: zodResolver(storeFormSchema),
    defaultValues: initialData || {
      name: "",
      currency: "USD",
      country: "",
      status: "incomplete",
    },
  });

  const [formError, setFormError] = useState<string | null>(null);
  const [formSuccess, setFormSuccess] = useState<boolean>(false);
  const [uploading, setUploading] = useState(false);
  
  // Country Selector States
  const [countries, setCountries] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isCountryOpen, setIsCountryOpen] = useState(false);

  const router = useNavigate();
  const storeCountry = watch("country");
  const storeCurrency = watch("currency");

  useEffect(() => {
    setCountries(getAllCountries());
  }, []);

  const filteredCountries = searchTerm.length > 0
    ? countries.filter(c => c.country.toLowerCase().includes(searchTerm.toLowerCase()))
    : countries;

  const handleCountrySelect = (country: any) => {
    setValue("country", country.country);
    setValue("currency", country.currency);
    setIsCountryOpen(false);
  };

  const handleImageChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) setValue("image", file);
    },
    [setValue],
  );

  const handleFormSubmit = async (data: StoreFormData) => {
    setFormError(null);
    setFormSuccess(false);
    setUploading(true);

    try {
      const file = data.image;
      if (file instanceof File) {
        const validation = validateFile(file);
        if (!validation.isValid) throw new Error(validation.error);

        toast.info("Uploading store logo...");
        const folder = "store_logos";
        const token = Cookies.get("auth_token") || "";
        if (!storeUrl || !token) throw new Error("Missing auth token or Store URL");
        
        const imageUrl = await uploadFileWithSignedUrl(file, folder, storeUrl, token);
        data.image_url = imageUrl;
      }
      delete data.image;

      await onSubmit({ ...data, url: storeUrl || '' } as StoreSchema);
      setFormSuccess(true);
      router("/app/stores");
    } catch (error) {
      setFormError((error as Error).message);
    } finally {
      setUploading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 sm:p-6 max-w-5xl">
      <form onSubmit={handleSubmit(handleFormSubmit)}>
        <Card>
          {formError && <AlertError message={formError} />}
          {formSuccess && <AlertSuccess message={`Store ${isEditing ? "updated" : "created"} successfully`} />}
          
          <CardHeader>
            <CardTitle>{isEditing ? "Edit Store" : "Add New Store"}</CardTitle>
          </CardHeader>

          <CardContent className="space-y-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="required">Store Name</Label>
                  <Input id="name" {...register("name")} placeholder="Enter store name" />
                  {errors.name && <p className="text-sm text-red-500">{errors.name.message}</p>}
                </div>

                {/* Updated Country Selector (Combobox Style) */}
                <div className="space-y-2">
                  <Label>Country</Label>
                  <Popover open={isCountryOpen} onOpenChange={setIsCountryOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        role="combobox"
                        aria-expanded={isCountryOpen}
                        className="w-full justify-between h-10 font-normal"
                      >
                        <div className="flex items-center">
                          <Globe size={18} className="mr-2 text-muted-foreground" />
                          <span className={storeCountry ? "text-foreground" : "text-muted-foreground"}>
                            {storeCountry || "Select a country"}
                          </span>
                        </div>
                        <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[var(--radix-popover-trigger-width)] p-0" align="start">
                      <Command>
                        <div className="flex items-center border-b px-3">
                          <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
                          <CommandInput
                            placeholder="Search countries..."
                            value={searchTerm}
                            onValueChange={setSearchTerm}
                            className="h-9"
                          />
                        </div>
                        <CommandList>
                          <CommandEmpty>No countries found.</CommandEmpty>
                          <CommandGroup className="max-h-64 overflow-auto">
                            {filteredCountries.map((c, idx) => (
                              <CommandItem
                                key={`${c.code}-${idx}`}
                                value={c.country}
                                onSelect={() => handleCountrySelect(c)}
                              >
                                <span className="mr-2 text-xs bg-muted rounded px-1 py-0.5">{c.code}</span>
                                {c.country}
                                {storeCountry === c.country && <Check className="ml-auto h-4 w-4 text-primary" />}
                              </CommandItem>
                            ))}
                          </CommandGroup>
                        </CommandList>
                      </Command>
                    </PopoverContent>
                  </Popover>
                </div>

                {/* Updated Currency Selector Style */}
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <InputGroup>
                    <InputGroupInput
                      placeholder="Select Country first"
                      value={storeCurrency || ''}
                      readOnly
                    />
                    <InputGroupAddon align="inline-end">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                            Change <ChevronDown className="size-3" />
                          </InputGroupButton>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="max-h-64 overflow-auto">
                           {/* Mapping a subset of common currencies if they want to manual override */}
                          {["USD", "KES", "EUR", "GBP", "UGX", "TZS", "RWF"].map((curr) => (
                            <DropdownMenuItem key={curr} onClick={() => setValue("currency", curr)}>
                              {curr}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </InputGroupAddon>
                  </InputGroup>
                </div>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="image_url">Store Logo</Label>
                  <Input id="image_url" type="file" accept="image/*" onChange={handleImageChange} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="category">Store Category</Label>
                  <Input id="category" {...register("category")} placeholder="e.g., Retail, Restaurant" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="opening_hours">Opening Hours</Label>
                  <Input id="opening_hours" {...register("opening_hours")} placeholder="e.g., Mon-Fri: 9AM-6PM" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input id="email" type="email" {...register("email")} placeholder="<EMAIL>" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone_number">Phone Number</Label>
                <Input id="phone_number" {...register("phone_number")} placeholder="****** 567 8900" />
              </div>
            </div>

            <div className="space-y-4">
              <Label className="text-muted-foreground uppercase text-xs font-bold tracking-wider">Address Information</Label>
              <div className="space-y-2">
                <Input id="street_address" {...register("street_address")} placeholder="Street Address" />
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                <Input id="city" {...register("city")} placeholder="City" />
                <Input id="state" {...register("state")} placeholder="State/Province" />
                <Input id="postal_code" {...register("postal_code")} placeholder="Postal Code" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea id="notes" {...register("notes")} placeholder="Notes..." rows={3} />
            </div>

            <Button type="submit" className="w-full h-11" disabled={uploading}>
              {uploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : isEditing ? "Update Store" : "Create Store"}
            </Button>
          </CardContent>
        </Card>
      </form>
    </div>
  );
}