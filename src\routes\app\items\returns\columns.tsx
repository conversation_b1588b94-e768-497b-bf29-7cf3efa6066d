"use client";

import { useState } from "react";
import type { ColumnDef } from "@tanstack/react-table"; import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

import type { Returns } from "@/lib/api/items/returns/models";
import { updateReturn } from "@/lib/api/items/returns/service";
import Cookies from "js-cookie";
import { RefundStatus } from "@/lib/types/refund_status";
import { toast } from "sonner"

import { Badge } from "@/components/ui/badge";
import type { Item } from "@/lib/api/items/models";
import type { Receipt } from "@/lib/api/receipts/models";
import type { RefundSchema } from "@/lib/api/sales/refunds/models";
import { createRefund } from "@/lib/api/sales/refunds/service";
import type { useNavigate } from "react-router";

export const getColumns = (router: ReturnType<typeof useNavigate>, items: Item[], receipts: Receipt[]): ColumnDef<Returns>[] => [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "item_id",
    header: "Item",
    cell: ({ row }) => {
      const item = items.find((item) => item.id === row.getValue("item_id"));
      return (
        <div
          className="font-semibold text-base cursor-pointer hover:text-primary"
          onClick={() => router(`/app/items/returns/${row.original.id}`)}
        >
          {item?.name}
        </div>
      );
    }
  },
  {
    accessorKey: "receipt_id",
    header: "Receipt",
    cell: ({ row }) => {
      const receipt = receipts.find((receipt) => receipt.id === row.getValue("receipt_id"));
      return receipt ? <div>#{receipt.receipt_number}</div> : null;
    },
  },
  {
    accessorKey: "quantity",
    header: "Qty",
    cell: ({ row }) => <div>{row.getValue("quantity")}</div>,
  },
  {
    accessorKey: "refund_status",
    header: "Status",
    cell: ({ row }) => {
      const returnItem = row.original;
      return <StatusCell returnItem={returnItem} router={router} />;
    },
  },
  {
    accessorKey: "timestamp",
    header: "Date",
    cell: ({ row }) => (
      <div>{new Date(row.getValue("timestamp")).toLocaleDateString()}</div>
    ),
  },
  {
    id: "refund",
    header: "Refund",
    cell: ({ row }) => {
      const returnItem = row.original;
      return <IssueRefundButton returnItem={returnItem} router={router} />;
    },
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const returnItem = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(returnItem.id.toString())}
            >
              Copy Return ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => router(`/app/items/returns/${returnItem.id}`)}
            >
              View Details
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => router(`/app/items/returns/${returnItem.id}/edit`)}
            >
              Edit Details
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];


const IssueRefundButton = ({
  returnItem,
  router
}: {
  returnItem: Returns;
  router: ReturnType<typeof useNavigate>;
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [refundAmount, setRefundAmount] = useState<number | null>(null);
  const token = Cookies.get("auth_token");
  const store_id = Number(Cookies.get("active_store"));

  const handleIssueRefund = async () => {
    if (!token || !store_id) {
      toast("Please login again");
      return;
    }

    if (refundAmount === null || refundAmount <= 0) {
      toast("Please enter a valid refund amount");
      return;
    }

    setIsProcessing(true);
    try {
      const updatedReturn: Returns = {
        ...returnItem,
        refund_status: RefundStatus.Processed,
      };

      await updateReturn(token, store_id, returnItem.id, updatedReturn);

      const refund_schema: RefundSchema = {
        transaction_id: returnItem.sale_id,
        return_id: returnItem.id,
        quantity_refunded: returnItem.quantity,
        amount_refunded: refundAmount,
        staff_id: returnItem.staff_id,
      };


      await createRefund(token, store_id, refund_schema);

      toast("Refund issued successfully");
      router(0);
    } catch (error) {
      toast((error as Error).message);
    } finally {
      setIsProcessing(false);
      setIsModalOpen(false);
    }
  };

  if (returnItem.refund_status !== RefundStatus.Pending) {
    return <div className="text-gray-400 text-sm">-</div>;
  }

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        disabled={isProcessing}
        variant="secondary"
        size="sm"
        className="bg-green-100 text-green-800 hover:bg-green-200"
      >
        {isProcessing ? "Processing..." : "Issue Refund"}
      </Button>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Enter Refund Amount</DialogTitle>
            <DialogDescription>
              Please enter the amount to be refunded for this return.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="refundAmount" className="text-right">
                Amount
              </Label>
              <Input
                id="refundAmount"
                type="number"
                value={refundAmount ?? ""}
                onChange={(e) => setRefundAmount(Number(e.target.value))}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={handleIssueRefund}
              disabled={isProcessing}
            >
              Confirm Refund
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

const StatusCell = ({
  returnItem,
  router
}: {
  returnItem: Returns;
  router: ReturnType<typeof useNavigate>;
}) => {
  const [_isUpdating, setIsUpdating] = useState(false);
  const token = Cookies.get("auth_token");
  const store_id = Number(Cookies.get("active_store"));

  const handleStatusChange = async (newStatus: RefundStatus) => {
    if (!token || !store_id) {
      toast(
        "Please login again",
      );
      return;
    }

    setIsUpdating(true);
    try {
      const updatedReturn: Returns = {
        ...returnItem,
        refund_status: newStatus,
      };

      await updateReturn(token, store_id, returnItem.id, updatedReturn);
      toast(
        `Return status changed to ${newStatus}`,
      );
      router(0);
    } catch (error) {
      toast(
        (error as Error).message,
      );
    } finally {
      setIsUpdating(false);
    }
  };

  return <StatusBadge status={returnItem.refund_status} />;
};

const StatusBadge = ({ status }: { status: RefundStatus }) => {
  const variantMap = {
    [RefundStatus.Pending]: "bg-yellow-100 text-yellow-800 hover:bg-yellow-200",
    [RefundStatus.Processed]: "bg-green-100 text-green-800 hover:bg-green-200",
    [RefundStatus.Cancelled]: "bg-red-100 text-red-800 hover:bg-red-200",
    [RefundStatus.Exchanged]: "bg-blue-100 text-blue-800 hover:bg-blue-200",
  };

  return (
    <Badge className={`${variantMap[status]} whitespace-nowrap`}>
      {status}
    </Badge>
  );
};