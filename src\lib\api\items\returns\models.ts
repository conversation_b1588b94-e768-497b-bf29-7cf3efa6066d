import { RefundStatus } from "@/lib/types/refund_status";

export interface ReturnSchema {
  sale_id: number;
  item_id: number;
  return_reason: string;
  receipt_id: number;
  staff_id: number;
  refund_status: RefundStatus;
  quantity: number;
}

export interface Returns {
  id: number;
  url: string;
  sale_id: number;
  item_id: number;
  return_reason: string;
  timestamp: string;
  receipt_id: number;
  staff_id: number;
  store_id: number;
  refund_status: RefundStatus;
  quantity: number;
}
