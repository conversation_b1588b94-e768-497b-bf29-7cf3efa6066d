import { BASE_URL } from "@/routes/configs/constants";
import type { Invoice, AddInvoicePayload, AddInvoiceItemPayload, InvoiceItem } from "./models";

export async function fetchInvoices(token: string, storeId: number): Promise<Invoice[]> {
    try {
        const res = await fetch(
            `${BASE_URL}/invoices/${storeId}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                }
            }
        );

        if (!res.ok) {
            console.error("Fetch failed:", res.status, await res.text());
            throw new Error("Failed to fetch invoices");
        }
        return res.json();
    } catch (error) {
        console.error("Error fetching invoices:", error);
        throw error;
    }
}

export async function createInvoice(token: string, storeId: number, payload: AddInvoicePayload): Promise<Invoice> {
    try {
        const response = await fetch(
            `${BASE_URL}/invoices/${storeId}`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to create invoice");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}


export async function updateInvoice(token: string, storeId: number, invoice_id: number, payload: Invoice): Promise<void> {
    try {
        const response = await fetch(
            `${BASE_URL}/invoices/${storeId}/${invoice_id}`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.text();
            console.error("Error response:", errorData);
            throw new Error(errorData || "Failed to update invoice");
        }

        const text = await response.text();
        if (text) {
            try {
                const res = JSON.parse(text);
                console.log(res);
                return res;
            } catch (jsonError) {
                console.error("Failed to parse JSON:", jsonError);
                throw new Error("Failed to parse JSON response");
            }
        } else {
            console.log("Empty response");
            return;
        }
    } catch (error) {
        console.error("Error in updateInvoice:", error);
        throw error;
    }
}

export async function deleteInvoice(token: string, storeId: number, invoice_id: number): Promise<void> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}`,
            {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete invoice");
        }
    } catch (error) {
        throw error;
    }
}

export async function getInvoiceById(token: string, storeId: number, invoice_id: number): Promise<Invoice> {
    try {
        const response = await fetch(
            // `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}`,
            `${BASE_URL}/invoices/${storeId}/${invoice_id}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}


export async function createInvoiceItems(authToken: string, storeId: number, invoice_id: number, payload: AddInvoiceItemPayload[]): Promise<InvoiceItem[]> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                    "X-Active-Store": storeId.toString(),
                },
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to create invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function fetchInvoiceItems(authToken: string, storeId: number, invoice_id: number): Promise<InvoiceItem[]> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                    "X-Active-Store": storeId.toString(),
                }
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice items");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function fetchInvoiceItem(authToken: string, storeId: number, invoice_id: number, item_id: number): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                    "X-Active-Store": storeId.toString(),
                }
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function updateInvoiceItem(authToken: string, storeId: number, invoice_id: number, item_id: number, payload: Partial<AddInvoiceItemPayload>): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                    "X-Active-Store": storeId.toString(),
                },
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

// export async function updateInvoiceItems(authToken: string, storeId: string, invoice_id: string, payload: InvoiceItem[]) : Promise<InvoiceItem>{
export async function updateInvoiceItems(authToken: string, storeId: number, invoice_id: number, payload: AddInvoiceItemPayload[]): Promise<InvoiceItem[]> {
    try {
        const response = await fetch(
            `${BASE_URL}/invoices/items/${storeId}/${invoice_id}`,
            {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                    "X-Active-Store": storeId.toString(),
                },
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }

}

export async function deleteInvoiceItem(authToken: string, storeId: number, invoice_id: number, item_id: number): Promise<void> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${authToken}`,
                    "X-Active-Store": storeId.toString(),
                }
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}


