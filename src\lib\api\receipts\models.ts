export interface Receipt {
    store: Store,
    url: string,
    total: number,
    salesperson: string,
    items: Item[],
    receipt_number: string,
    id: number,
    created_at: string,
}


export interface Store {
    name: string,
    address?: string,
    phone?: string,
    postal_code?: string,
    country?: string,
    city?: string,
    currency?: string,
}

export interface Item {
    name: string,
    amount: number,
    quantity: number,
    store_id: number,
    has_vat?: boolean,
    vat_percentage?: number,
}
