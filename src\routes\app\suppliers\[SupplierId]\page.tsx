"use client";

import { deleteSupplier, getSupplier } from '@/lib/api/suppliers/service';
import type { Supplier } from '@/lib/api/suppliers/models';
// import { notFound } from 'next/navigation';
import Cookies from 'js-cookie';
import { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from "react-router";
import { Button } from '@/components/ui/button';
import { use } from 'react';


export default function SupplierPage() {
  const router = useNavigate();
  const supplierId = Number(useParams().SupplierId);
  const [supplier, setSupplier] = useState<Supplier | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const authToken = Cookies.get("auth_token");
  const storeId = Number(Cookies.get("active_store"));

  useEffect(() => {

    const fetchData = async () => {
      if (!authToken || !storeId) {
        // notFound();
        return;
      }

      try {
        const fetchedSupplier = await getSupplier(authToken, storeId, supplierId);
        setSupplier(fetchedSupplier);
      } catch (err: any) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [supplierId, authToken, storeId]);


  const handleDelete = async () => {

    if (!authToken || !storeId) {
      alert("Authentication error. Please log in again.");
      return;
    }

    try {
      await deleteSupplier(authToken, storeId, supplierId);

      router('/app/suppliers');
      router(0);
    } catch (err: any) {
      console.error("Delete error:", err);
      setError(err);
    } finally {
      setShowDeleteModal(false);
    }
  };


  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading supplier details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/suppliers">
              <Button variant="link">Return to Suppliers</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!supplier) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Supplier Not Found</h2>
          <p className="text-gray-600 text-center">The requested supplier could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/suppliers">
              <Button variant="link">Return to Suppliers</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }


  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500">
            <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link to="/app/suppliers" className="hover:text-blue-600">Suppliers</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">{supplier.name}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">{supplier.name}</h1>
          <p className="text-gray-500 text-sm">Supplier Details</p>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Supplier Information</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 font-medium">Name</p>
                  <p className="text-gray-800 text-sm">{supplier.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Contact Person</p>
                  <p className="text-gray-800 text-sm">{supplier.contact_person}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Phone Number</p>
                  <p className="text-gray-800 text-sm">{supplier.phone_number || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Notes</p>
                  <p className="text-gray-800 text-sm">{supplier.notes || '—'}</p>
                </div>
              </div>
            </div>
            <div>
              <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Address</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 font-medium">Address</p>
                  <p className="text-gray-800 text-sm">{supplier.address || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">City</p>
                  <p className="text-gray-800 text-sm">{supplier.city || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">State</p>
                  <p className="text-gray-800 text-sm">{supplier.state || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Country</p>
                  <p className="text-gray-800 text-sm">{supplier.country || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Postal Code</p>
                  <p className="text-gray-800 text-sm">{supplier.postal_code || '—'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="mt-6 flex justify-end gap-3">

            <Link to={`/app/suppliers/${supplierId}/edit`}>
              <Button variant="default" className="px-4 py-2">Edit</Button>
            </Link>
            <Button
              variant="destructive"
              onClick={() => setShowDeleteModal(true)}
              className="px-4 py-2"
            >
              Delete
            </Button>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 animate-fade-in">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="mt-5 text-lg font-medium text-gray-900">Delete Supplier</h3>
              <p className="mt-2 text-sm text-gray-500">
                Are you sure you want to delete "{supplier.name}"?  This action cannot be undone.
              </p>
            </div>
            <div className="mt-6 flex justify-end gap-3">
              <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
