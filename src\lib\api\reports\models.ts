
// export interface LowStockItems {
//   item_id: string,
//   item_name: string,
//   quantity: number,
//   store_id: string
// }


export interface LowStockItem {
  item_id: string;
  item_name: string;
  quantity: number;
  store_id: string;
}

// export interface EmployeePerformance {
//   employee_id: string;
//   employee_name: string;
//   number_of_sales: number;
//   total_sales: number;
// }

export interface EmployeePerformance {
  employee_id: string;
  employee_name: string;
  total_sales: number | null;
  number_of_sales: number | null;
}

export interface MonthlyFinancialData {
  month: string;
  profit: number;
  loss: number;
}

export interface DailyAverageSales {
  average_sale: number;
  day_of_week: string;
}

export interface ProductPerformance {
  category: string;
  quantity: number;
}

export interface DailyTotalSales {
  date: string;
  total: number;
}

export interface DailyStoreAggregatedAverageSales {
  monday: number;
  tuesday: number;
  wednesday: number;
  thursday: number;
  friday: number;
  saturday: number;
  sunday: number;
}

export interface StoreMetrics {
  average_transaction_value: number;
  sales: number;
  store_id: number;
  store_name: string;
  transactions: number;
}

export interface DashboardStats {
  items_sold: number;
  items_sold_change: number;
  total_revenue: number;
  total_revenue_change: number;
  total_transactions: number;
  total_transactions_change: number;
  transactions_last_hour: number;
  transactions_last_hour_change: number;
}
