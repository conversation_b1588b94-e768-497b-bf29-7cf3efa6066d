"use client";

import { getExchange, deleteExchange } from '@/lib/api/items/exchanges/service';
import type { Exchange } from '@/lib/api/items/exchanges/models';
import Cookies from 'js-cookie';
import { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from "react-router";
import { Button } from '@/components/ui/button';
import type { Item } from '@/lib/api/items/models';
import type { User } from '@/lib/api/users/models';
import type { Store } from '@/lib/api/retailStores/models';
import { fetchItem } from '@/lib/api/items/service';
import { fetchUsersWithRoles } from '@/lib/api/roles/service';
import { getStore } from '@/lib/api/retailStores/service';
import type { UsersWithRolesResponse } from '@/lib/api/roles/models';
import { use } from 'react';


export default function ExchangePage() {
  const router = useNavigate();
  const exchangeId = Number(useParams().ExchangeId);
  const [exchange, setExchange] = useState<Exchange | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const [originalItem, setOriginalItem] = useState<Item>();
  const [exchangedItem, setExchangedItem] = useState<Item>();
  const [staffMembers, setStaffMembers] = useState<UsersWithRolesResponse>();
  const [store, setStore] = useState<Store>();

    useEffect(() => {
    const authToken = Cookies.get("auth_token");
    const storeId = Number(Cookies.get("active_store"));

    const fetchData = async () => {
      if (!authToken || !storeId) {
        // notFound(); // Or handle as a client-side error
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const fetchedExchange = await getExchange(authToken, storeId, exchangeId);
        setExchange(fetchedExchange);

        if (fetchedExchange) {
          const [
            originalItem,
            exchangedItem,
            staffMembers,
            store,
          ] = await Promise.all([
            fetchItem(authToken, storeId, fetchedExchange.original_item_id),
            fetchedExchange.exchanged_with_item_id
              ? fetchItem(authToken, storeId, fetchedExchange.exchanged_with_item_id)
              : Promise.resolve(null),
            fetchUsersWithRoles(storeId, authToken),
            getStore(storeId, authToken),
          ]);

          setOriginalItem(originalItem);
          setExchangedItem(exchangedItem as Item);
          setStaffMembers(staffMembers);
          setStore(store);
        }
      } catch (err: any) {
        setError(err);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [exchangeId]);

  const handleDelete = async () => {
    const authToken = Cookies.get("auth_token");
    const storeId = Number(Cookies.get("active_store"));

    if (!authToken || !storeId) {
      setError(new Error("Authentication token or store ID is missing."));
      return;
    }

    if (confirm("Are you sure you want to delete this exchange?")) {
      try {
        setLoading(true);
        await deleteExchange(authToken, storeId, exchangeId);
        router("/app/items/exchanges");
      } catch (err: any) {
        setError(err);
        setLoading(false);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading exchange details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/items/exchanges">
              <Button variant="link">Return to Exchanges</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!exchange) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Exchange Not Found</h2>
          <p className="text-gray-600 text-center">The requested exchange could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/items/exchanges">
              <Button variant="link">Return to Exchanges</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }
    const formatDate = (dateString: string) => {
      return new Date(dateString).toLocaleString(); // Or use toLocaleDateString() for date only
    };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500">
            <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link to="/app/items/exchanges" className="hover:text-blue-600">Exchanges</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">Exchange {exchange.receipt_number}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">Exchange Details</h1>
          <p className="text-gray-500 text-sm">Exchange ID: {exchange.id}</p>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-sm">
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Exchange Information</h2>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Original Sale ID</p>
                    <p className="text-gray-800 text-sm">#{exchange.original_sale_id}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Original Item ID</p>
                    <p className="text-gray-800 text-sm">{originalItem?.name || '—'}</p>
                  </div>
                  {/* <div>
                    <p className="text-sm text-gray-500 font-medium">Transaction ID</p>
                    <p className="text-gray-800 text-sm">{exchange.transaction_id || '—'}</p>
                  </div> */}
                  <div>
                      <p className="text-sm text-gray-500 font-medium">Store ID</p>
                      <p className="text-gray-800 text-sm">{store?.name || '—'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Quantity Exchanged</p>
                    <p className="text-gray-800 text-sm">{exchange.quantity_exchanged}</p>
                  </div>
                  <div>
                      <p className="text-sm text-gray-500 font-medium">Exchange With Item ID</p>
                      <p className="text-gray-800 text-sm">{exchangedItem?.name || '—'}</p>
                  </div>
                  <div>
                      <p className="text-sm text-gray-500 font-medium">Exchange Value</p>
                      <p className="text-gray-800 text-sm">{exchange.exchange_value || '—'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Exchange Reason</p>
                    <p className="text-gray-800 text-sm">{exchange.exchange_reason || '—'}</p>
                  </div>
                </div>
              </div>
              <div>
                <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Additional Details</h2>
                <div className="space-y-3">

                  <div>
                    <p className="text-sm text-gray-500 font-medium">Timestamp</p>
                    <p className="text-gray-800 text-sm">{formatDate(exchange.timestamp)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Receipt Number</p>
                    <p className="text-gray-800 text-sm">{exchange.receipt_number}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Comments</p>
                    <p className="text-gray-800 text-sm">{exchange.comments || '—'}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 font-medium">Staff ID</p>
                    <p className="text-gray-800 text-sm">{staffMembers?.users.find((user) => user.user_id === exchange.staff_id)?.name || '—'}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Action Buttons Footer */}
          <div className="p-4 border-t border-gray-200 flex justify-end gap-3">
            <Button variant="outline" className="px-4 py-2" disabled={loading} onClick={() => { router(`/app/items/exchanges/${exchangeId}/edit`); }}>
              Edit
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              className="px-4 py-2"
              disabled={loading}
            >
              Delete
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}