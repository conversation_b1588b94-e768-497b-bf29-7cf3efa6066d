"use client";

import { StoreForm } from "@/routes/app/stores/StoreForm";
import type { StoreSchema } from "@/lib/api/retailStores/models";
import { createStore } from "@/lib/api/retailStores/service";
import Cookies from "js-cookie";

export default function CreateStorePage() {

  const token = Cookies.get("auth_token");
  const storeUrl = Cookies.get("active_store_url");

  const handleSubmit = async (data: StoreSchema) => {
    if (!token) {
      throw new Error("Authentication token is missing.");
    }
    await createStore(data, token);
  };

  return <StoreForm onSubmit={handleSubmit} storeUrl={storeUrl} />;
}