"use client";

import { getRetailStoreClient } from '@/lib/api/clients/service';
import type { Customer } from '@/lib/api/clients/models';
// import { notFound, useParams } from 'next/navigation';
import Cookies from 'js-cookie';
import { useState, useEffect } from 'react';
import { Link, useNavigate, useParams } from "react-router";
import { Button } from '@/components/ui/button';
import { getStore } from '@/lib/api/retailStores/service';
import type { Store } from '@/lib/api/retailStores/models';
import { fetchCustomers } from '@/lib/api/customers/service';
import { use } from 'react';


export default function CustomerPage() {
  const router = useNavigate();

  const customerId = useParams().CustomerId ?? "0000-0000-0000-0000-000000000000";
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [store, setStore] = useState<Store | null>(null);

  useEffect(() => {
    const authToken = Cookies.get("auth_token");
    const storeId = Number(Cookies.get("active_store"));

    const fetchData = async () => {
      if (!authToken || !storeId) {
        //notFound(); // Or handle as client-side error
        return;
      }

      setLoading(true);
      setError(null);

      try {
        const customerIdNum = Number(customerId);
        if (!customerIdNum) {
          throw new Error('Invalid customer ID');
        }
        const [fetchedCustomer, fetchedStore] = await Promise.all([
          getRetailStoreClient(authToken, storeId, customerIdNum),
          getStore(storeId, authToken),
        ]);
        setCustomer(fetchedCustomer);
        setStore(fetchedStore);
      } catch (err) {
        if (err instanceof Error) {
          setError(err);
        } else {
          setError(new Error('An unknown error occurred.'));
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [customerId]);

    const handleDelete = async () => {
        const authToken = Cookies.get("auth_token");
        const storeId = Cookies.get("active_store");
        if (!authToken || !storeId) {
          alert("Authentication error. Please log in again.");
          return;
        }
    try {
      const response = await fetch(`/api/customers/${customerId}`, { // Corrected API endpoint
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete customer');
      }

        router('/app/customers');
        router(0)
    } catch (err) {
      if (err instanceof Error) {
        console.error("Delete error:", err);
        setError(err);
      } else {
        setError(new Error('An unknown error occurred.'));
      }
    } finally {
      setShowDeleteModal(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 rounded-full border-4 border-t-blue-500 border-gray-200 animate-spin"></div>
          <p className="mt-4 text-gray-600 font-medium">Loading customer details...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-red-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Error</h2>
          <p className="text-gray-600 text-center">{error.message}</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/customers">
              <Button variant="link">Return to Customers</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="min-h-screen flex justify-center items-center bg-gray-50">
        <div className="bg-white p-8 rounded-lg shadow-md max-w-md w-full">
          <div className="text-amber-500 flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-center mb-2">Customer Not Found</h2>
          <p className="text-gray-600 text-center">The requested customer could not be found.</p>
          <div className="mt-6 flex justify-center">
            <Link to="/app/customers">
              <Button variant="link">Return to Customers</Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Breadcrumb */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3">
          <div className="flex text-sm text-gray-500">
            <Link to="/app/dashboard" className="hover:text-blue-600">Dashboard</Link>
            <span className="mx-2">/</span>
            <Link to="/app/customers" className="hover:text-blue-600">Customers</Link>
            <span className="mx-2">/</span>
            <span className="text-gray-700 font-medium">{customer.name}</span>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 pt-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-800">{customer.name}</h1>
          <p className="text-gray-500 text-sm">Customer Details</p>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-xl shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h2 className="text-lg font-bold mb-4 text-gray-800 border-b pb-2">Customer Information</h2>
              <div className="space-y-3">
                <div>
                  <p className="text-sm text-gray-500 font-medium">Name</p>
                  <p className="text-gray-800 text-sm">{customer.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Email</p>
                  <p className="text-gray-800 text-sm">{customer.email || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Phone</p>
                  <p className="text-gray-800 text-sm">{customer.phone || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Store ID</p>
                  <p className="text-gray-800 text-sm">{store?.name || '—'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-500 font-medium">Created At</p>
                  <p className="text-gray-800 text-sm">{formatDate(customer.created_at)}</p>
                </div>
              </div>
            </div>
            {/* You can add more sections here if you have more customer data */}
          </div>

          {/* Actions */}
          <div className="mt-6 flex justify-end gap-3">
            <Link to={`/app/customers/${customerId}/edit`}>
                <Button variant="default" className="px-4 py-2">Edit</Button>
            </Link>
            <Button variant="destructive" onClick={() => setShowDeleteModal(true)}  className="px-4 py-2">
              Delete
            </Button>
          </div>
        </div>
      </div>
          {/* Delete confirmation modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6 animate-fade-in">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
              </div>
              <h3 className="mt-5 text-lg font-medium text-gray-900">Delete Customer</h3>
              <p className="mt-2 text-sm text-gray-500">
                Are you sure you want to delete &quot;{customer.name}&quot;? This action cannot be undone.
              </p>
            </div>
            <div className="mt-6 flex justify-end gap-3">
              <Button variant="secondary" onClick={() => setShowDeleteModal(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleDelete}>
                Delete
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
