import { BASE_URL } from "@/routes/configs/constants";
import type { Exchange, ExchangeSchema } from "./models";

export async function fetchExchanges(
  authToken: string,
  storeId: number,
): Promise<Exchange[]> {
  const url = `${BASE_URL}/items/exchanges/${storeId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function createExchange(
  authToken: string,
  storeId: number,
  payload: ExchangeSchema,
): Promise<Exchange> {
  const url = `${BASE_URL}/items/exchanges/${storeId}`;
  try {
    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function getExchange(
  authToken: string,
  storeId: number,
  exchangeId: number,
): Promise<Exchange> {
  const url = `${BASE_URL}/items/exchanges/${storeId}/${exchangeId}`;
  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function updateExchange(
  authToken: string,
  storeId: number,
  exchangeId: number,
  payload: ExchangeSchema,
): Promise<Exchange> {
  const url = `${BASE_URL}/items/exchanges/${storeId}/${exchangeId}`;

  try {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(payload),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Login failed");
    }
    return await response.json();
  } catch (error) {
    throw error;
  }
}

export async function deleteExchange(
  authToken: string,
  storeId: number,
  exchangeId: number,
): Promise<void> {
  const url = `${BASE_URL}/items/exchanges/${storeId}/${exchangeId}`;
  try {
    const response = await fetch(url, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Deleting exchanged item failed");
    }
  } catch (error) {
    throw error;
  }
}
