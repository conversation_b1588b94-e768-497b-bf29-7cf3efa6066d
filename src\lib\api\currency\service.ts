
import { BASE_URL } from "@/routes/configs/constants";
import type { CurrencySchema } from "./models";
import Cookies from "js-cookie";

export async function getCurrency(authToken: string) {
    try{
        const storeId = Number(Cookies.get("active_store"));
        const response = await fetch(`${BASE_URL}/currency`, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
                "X-Active-Store": storeId.toString(),
            },
        });
        if (response.ok){
            return await response.json();
        }
        else{
            const errorMessage = await response.json();
            throw new errorMessage(`Failed to fetch currency: ${errorMessage.error || 'Unknown error'}`);
        }
    }
    catch (e){
        console.error(e);
        return null;
    }
}

export async function createCurrency(currency: CurrencySchema, authToken: string){
    try{
        const storeId = Number(Cookies.get("active_store"));
        const response = await fetch(`${BASE_URL}/currency`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${authToken}`,
                "X-Active-Store": storeId.toString(),
            },
            body: JSON.stringify(currency),
        });
        if (response.status === 201){
            return await response.json();
        }
        else{
            const errorMessage = await response.json();
            throw new errorMessage(`Failed to create currency: ${errorMessage.error || 'Unknown error'}`);
        }
    }
    catch (e){
        console.error(e);
        return null;
    }
}