import { BASE_URL } from "@/routes/configs/constants";
import type { Receipt } from "./models";

export async function fetchReceipts(authToken: string, storeId: number)
: Promise<Receipt[]>{
  const url = `${BASE_URL}/sales/receipts/${storeId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch receipts: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching receipts:", error);
    throw error;
  }
}

export async function fetchReceipt(authToken: string, storeId: number, receiptId: number)
: Promise<Receipt>{
  const url = `${BASE_URL}/sales/receipts/${storeId}/${receiptId}`;
  const headers = {
    Authorization: `Bearer ${authToken}`,
    "Content-Type": "application/json",
    "X-Active-Store": storeId.toString(),
  };

  try {
    const response = await fetch(url, { headers });

    if (!response.ok) {
      throw new Error(`Failed to fetch receipt: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching receipt:", error);
    throw error;
  }
}
