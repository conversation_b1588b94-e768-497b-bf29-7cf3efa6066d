import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>xi<PERSON> } from 'recharts';
import { type ChartConfig, ChartContainer, ChartTooltip } from '@/components/ui/chart';
import { type EmployeePerformance } from '@/lib/api/reports/models';
import { formatCurrency } from '@/lib/utils/currency';

interface EmployeeSalesPerformanceProps {
    employeeData: EmployeePerformance[] | null;
}

const chartConfig = {
  totalSales: {
    label: "Total Sales",
    color: "hsl(210, 100%, 60%)", // Similar to rgb(53, 162, 235)
  },
} satisfies ChartConfig;

const EmployeeSalesPerformance = ({ employeeData }: EmployeeSalesPerformanceProps) => {
    if (!employeeData) {
        return <div>Loading... or Error</div>;
    }

    // Transform data for Recharts
    const chartData = employeeData.map((employee, index) => ({
        employeeName: employee.employee_name,
        // Truncate long names for better display
        displayName: employee.employee_name.length > 10 
            ? `${employee.employee_name.slice(0, 10)}...` 
            : employee.employee_name,
        totalSales: employee.total_sales || 0,
        index: index,
    }));

    return (
        <div>
            <h2 className="text-lg font-semibold mb-4">Employee Sales Performance</h2>
            <ChartContainer config={chartConfig} className="h-[250px] w-full">
                <LineChart
                    data={chartData}
                    margin={{
                        top: 20,
                        right: 30,
                        left: 20,
                        bottom: 60,
                    }}
                >
                    <XAxis
                        dataKey="displayName"
                        tickLine={false}
                        axisLine={false}
                        className="text-xs"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                    />
                    <YAxis
                        tickLine={false}
                        axisLine={false}
                        tickFormatter={(value) => `${formatCurrency(value)}`}
                        className="text-xs"
                    />
                    <Line
                        type="monotone"
                        dataKey="totalSales"
                        stroke="var(--color-totalSales)"
                        strokeWidth={2}
                        dot={{
                            fill: "var(--color-totalSales)",
                            strokeWidth: 2,
                            r: 4,
                        }}
                        activeDot={{
                            r: 6,
                            fill: "var(--color-totalSales)",
                        }}
                    />
                    <ChartTooltip
                        content={({ active, payload }) => {
                            if (active && payload && payload.length) {
                                const data = payload[0].payload;
                                return (
                                    <div className="rounded-lg border bg-background p-2 shadow-md">
                                        <div className="grid gap-2">
                                            <div className="flex flex-col">
                                                <span className="text-sm font-medium">
                                                    {data.employeeName}
                                                </span>
                                                <span className="text-sm text-muted-foreground">
                                                    Total Sales: ${data.totalSales?.toLocaleString()}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                );
                            }
                            return null;
                        }}
                    />
                </LineChart>
            </ChartContainer>
        </div>
    );
};

export default EmployeeSalesPerformance;