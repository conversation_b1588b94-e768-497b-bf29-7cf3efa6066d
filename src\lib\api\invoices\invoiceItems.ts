import { BASE_URL } from "@/routes/configs/constants";
import type { InvoiceItem, AddInvoiceItemPayload } from "./models";

export async function fetchInvoiceItems(token: string, storeId: number, invoice_id: number): Promise<InvoiceItem[]> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                credentials: "include",
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice items");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function createInvoiceItem(
    token: string, 
    storeId: number, 
    invoice_id: number, 
    payload: AddInvoiceItemPayload
): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items`,
            {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                credentials: "include",
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to create invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function updateInvoiceItem(
    token: string, 
    storeId: number, 
    invoice_id: number,
    item_id: number,
    payload: Partial<AddInvoiceItemPayload>
): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                credentials: "include",
                body: JSON.stringify(payload)
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to update invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}

export async function deleteInvoiceItem(
    token: string, 
    storeId: number, 
    invoice_id: number,
    item_id: number
): Promise<void> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "DELETE",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                credentials: "include",
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to delete invoice item");
        }
    } catch (error) {
        throw error;
    }
}

export async function getInvoiceItemById(
    token: string, 
    storeId: number, 
    invoice_id: number,
    item_id: number
): Promise<InvoiceItem> {
    try {
        const response = await fetch(
            `${BASE_URL}/stores/${storeId}/invoices/${invoice_id}/items/${item_id}`,
            {
                method: "GET",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`,
                    "X-Active-Store": storeId.toString(),
                },
                credentials: "include",
            }
        );

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.error || "Failed to fetch invoice item");
        }
        return response.json();
    } catch (error) {
        throw error;
    }
}