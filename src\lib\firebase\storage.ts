import { ref, deleteObject, getDownloadURL } from "firebase/storage";
import { storage } from "./config";
import { BASE_URL } from "@/routes/configs/constants";

interface BackendUploadResponse {
  location: string; // This 'location' field contains the signed URL
}

/**
 * Makes an API call to your backend to request a signed URL for file upload.
 * @param storeUrl The ID of the store.
 * @param folder The folder path (e.g., 'products', 'store_logos').
 * @param filename The final filename for the object in GCS.
 * @param authToken The authentication token for backend authorization.
 * @param contentType The MIME type of the file (e.g., 'image/png').
 * @returns Promise<BackendUploadResponse> containing the signed URL (in 'location' field).
 */
async function requestSignedUploadUrlFromBackend(
  storeUrl: string,
  folder: string,
  filename: string,
  authToken: string,
  contentType: string
): Promise<BackendUploadResponse> {
  const backendApiUrl = `${BASE_URL}/signed_upload/${storeUrl}/${folder}/${filename}`;

  try {
    const response = await fetch(backendApiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Failed to get signed URL from backend: ${errorData.message || response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error fetching signed URL from backend:', error);
    throw new Error(`Failed to connect to backend for signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


/**
 * Uploads a file directly to Google Cloud Storage using a pre-signed URL.
 * @param file The file object to upload.
 * @param signedUrl The pre-signed URL obtained from your backend.
 * @returns Promise<string> The public URL of the uploaded file.
 */
async function uploadFileDirectlyToGCS(file: File, signedUrl: string): Promise<void> {
  try {
    const response = await fetch(signedUrl, {
      method: 'PUT',
      body: file,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Error response from GCS:', errorText);
      throw new Error(`Upload failed with status ${response.status}: ${errorText}`);
    }
  } catch (error) {
    console.error('Error uploading file to GCS:', error);
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('This might be a network or CORS error.');
    }
    throw new Error(`Failed to upload file using signed URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}


/**
 * Upload a single file to Firebase Storage via a signed URL.
 * This function orchestrates the request for a signed URL from the backend
 * and then performs the direct upload to GCS.
 * @param file - The file to upload
 * @param folder - The folder path (e.g., 'products', 'store_logos')
 * @param storeUrl - The store ID for organizing files
 * @param authToken - The authentication token for backend authorization
 * @param fileName - Optional custom filename, if not provided, one will be generated.
 * @returns Promise<string> - The public download URL of the uploaded file.
 */
export async function uploadFileWithSignedUrl(
  file: File,
  folder: string,
  storeUrl: string,
  authToken: string,
  fileName?: string
): Promise<string> {
  try {
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    let finalFileName = fileName || `${timestamp}_${Math.random().toString(36).substring(2)}.${fileExtension}`;

    if (folder === 'store_logos') {
      finalFileName = `${storeUrl}.${fileExtension}`;
    }

    const backendResponse = await requestSignedUploadUrlFromBackend(
      storeUrl,
      folder,
      finalFileName,
      authToken,
      file.type
    );

    const signedUrl = backendResponse.location;

    // Upload file using the signed URL.
    await uploadFileDirectlyToGCS(file, signedUrl);

    // After upload, get the download URL from Firebase Storage.
    // The path must match the one the backend used to create the signed URL.
    const storagePath = `${folder}/${storeUrl}/${finalFileName}`;
    const fileRef = ref(storage, storagePath);
    const downloadUrl = await getDownloadURL(fileRef);

    return downloadUrl;
  } catch (error) {
    console.error('Error orchestrating file upload:', error);
    throw new Error(`Failed to upload file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Upload multiple files to Firebase Storage via signed URLs.
 * @param files - Array of files to upload
 * @param folder - The folder path
 * @param storeUrl - The store ID for organizing files
 * @param authToken - The authentication token for backend authorization
 * @returns Promise<string[]> - Array of public download URLs
 */
export async function uploadMultipleFiles(
  files: File[],
  folder: string,
  storeUrl: string,
  authToken: string
): Promise<string[]> {
  try {
    const uploadPromises = files.map(file => uploadFileWithSignedUrl(file, folder, storeUrl, authToken));
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading multiple files:', error);
    throw error;
  }
}

/**
 * Delete a file from Firebase Storage.
 * @param fileUrl - The download URL of the file to delete.
 */
export async function deleteFileFromFirebase(fileUrl: string): Promise<void> {
  try {
    const url = new URL(fileUrl);
    // Extracts path from a URL like: https://firebasestorage.googleapis.com/v0/b/bucket-name/o/path%2Fto%2Ffile.jpg?alt=media
    const pathMatch = url.pathname.match(/\/o\/(.+)$/);
    if (!pathMatch) {
      throw new Error('Invalid Firebase Storage URL. Could not extract file path.');
    }

    const fullPath = decodeURIComponent(pathMatch[1]);
    const storageRef = ref(storage, fullPath);

    await deleteObject(storageRef);
  } catch (error) {
    console.error('Error deleting file from Firebase:', error);
    throw error;
  }
}

/**
 * File size limit for uploads (10MB).
 */
export const MAX_FILE_SIZE = 10 * 1024 * 1024;

/**
 * Validate file before upload.
 * @param file - File to validate
 * @param allowedTypes - Array of allowed MIME types
 * @param maxSize - Maximum file size in bytes
 */
export function validateFile(
  file: File,
  allowedTypes: string[] = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  maxSize: number = MAX_FILE_SIZE
): { isValid: boolean; error?: string } {
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `File type not allowed. Allowed types: ${allowedTypes.join(', ')}`
    };
  }

  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File size too large. Maximum size: ${(maxSize / 1024 / 1024).toFixed(1)}MB`
    };
  }

  return { isValid: true };
}
