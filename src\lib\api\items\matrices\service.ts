import { BASE_URL } from "@/routes/configs/constants";
import type { ItemMatrix, ItemMatrixSchema } from "./models";

export async function getItemMatrices(
  authToken: string,
  storeId: number,
): Promise<ItemMatrix[]> {
  try {
    const response = await fetch(`${BASE_URL}/items/matrices/${storeId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to fetch item matrices: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function getItemMatrix(
  authToken: string,
  storeId: number,
  matrixId: number,
): Promise<ItemMatrix> {
  try {
    const response = await fetch(
      `${BASE_URL}/items/matrices/${storeId}/${matrixId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to fetch item matrix: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function createItemMatrix(
  authToken: string,
  matrix: ItemMatrixSchema,
  storeId: number,
): Promise<ItemMatrix> {
  try {
    const response = await fetch(`${BASE_URL}/items/matrices/${storeId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(matrix),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to create item matrix: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function updateItemMatrix(
  authToken: string,
  storeId: number,
  matrixId: number,
  matrix: ItemMatrixSchema,
): Promise<ItemMatrix> {
  try {
    const response = await fetch(
      `${BASE_URL}/items/matrices/${storeId}/${matrixId}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
        body: JSON.stringify(matrix),
      },
    );
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to update item matrix: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function deleteItemMatrix(
  authToken: string,
  matrixId: number,
  storeId: number,
): Promise<void> {
  try {
    const response = await fetch(
      `${BASE_URL}/items/matrices/${storeId}/${matrixId}`,
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${authToken}`,
          "X-Active-Store": storeId.toString(),
        },
      },
    );
    if (!response.ok) {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to delete item matrix: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}
