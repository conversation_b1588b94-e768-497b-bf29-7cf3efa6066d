export interface SellItemSchema {
  item_id: number;
  price: number;
  quantity: number;
  store_id: number;
  has_vat?: boolean;
  vat_percentage?: number;
}

export interface MpesaPayload {
  amount: number;
  phone_number: string;
}

export type PaymentMethod = "mpesa" | "card" | "cash";

export interface SellItemsSchema {
  items: SellItemSchema[];
  mpesa_payload: MpesaPayload | null;
  payment_method: PaymentMethod | null;
}

export interface Sale {
  id: number,
  url: string,
  item_id: number,
  salesperson_id: number,
  sale_time: string,
  quantity: number,
  total: number,
  items_remaining: number,
  store_id: number,
  receipt_id: number,
  status: SaleStatus,
  date_updated?: string,
  has_vat?: boolean,
  vat_percentage?: number,
}

export enum SaleStatus {
  Completed = "completed",
  Returned = "returned",
  Pending = "pending",
  Canceled = "canceled",
}
