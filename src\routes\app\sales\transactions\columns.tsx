import type { ColumnDef } from "@tanstack/react-table";

import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpDown, MoreHorizontal } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { PaymentTransaction } from "@/lib/api/sales/transactions/models";
import { useNavigate } from "react-router";

export const getColumns = (_navigate: ReturnType<typeof useNavigate>): ColumnDef<PaymentTransaction>[] => [
  {
    accessorKey: "payment_method",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Payment Method
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("payment_method")}</div>,
    enableSorting: true,
  },
  // {
  //   accessorKey: "merchant_request_id",
  //   header: ({ column }) => (
  //     <Button
  //       variant="ghost"
  //       onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
  //     >
  //       Merchant Request ID
  //       <ArrowUpDown />
  //     </Button>
  //   ),
  //   cell: ({ row }) => <div>{row.getValue("merchant_request_id") || "N/A"}</div>,
  //   enableSorting: true,
  // },
  {
    accessorKey: "mpesa_receipt_number",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        M-Pesa Receipt
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("mpesa_receipt_number") ?? "N/A"}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "card_type",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Card Info
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const paymentMethod = row.getValue("payment_method") as string;
      if (paymentMethod === "CARD") {
        const cardType = row.getValue("card_type") as string;
        const lastFour = row.getValue("card_last_four") as string;
        return <div>{cardType} **** {lastFour}</div>;
      }
      return <div>N/A</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "transaction_date",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Transaction Date
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      const rawDate = row.getValue<string>("transaction_date");
      const formattedDate = new Date(rawDate).toLocaleDateString(undefined, {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric",
      });
      return <div>{formattedDate}</div>;
    },
    enableSorting: true,
  },
  {
    accessorKey: "phone_number",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Phone Number
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>{row.getValue("phone_number") || "N/A"}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "amount",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Amount
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => <div>KES {row.getValue("amount")}</div>,
    enableSorting: true,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <Button
        variant="ghost"
        onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
      >
        Status
        <ArrowUpDown />
      </Button>
    ),
    cell: ({ row }) => {
      // Normalize status to lowercase for comparison
      const statusRaw = row.original.status as string;
      const status = statusRaw.toLowerCase();
      let color = "";
      let label: string = statusRaw.charAt(0).toUpperCase() + statusRaw.slice(1);
      switch (status) {
        case "completed":
          color = "bg-green-100 text-green-800";
          break;
        case "pending":
          color = "bg-yellow-100 text-yellow-800";
          break;
        case "returned":
          color = "bg-blue-100 text-blue-800";
          break;
        case "canceled":
        case "cancelled":
          color = "bg-red-100 text-red-800";
          break;
        default:
          color = "bg-gray-100 text-gray-800";
      }
      return (
        <span className={`px-2 py-1 rounded text-xs font-semibold ${color}`}>{label}</span>
      );
    },
    enableSorting: true,
  },
  {
    id: "actions",
    enableHiding: false,
    cell: ({ row }) => {
      const transaction = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(transaction.id.toString())}
            >
              Copy Transaction ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View Details</DropdownMenuItem>
            {/* Add other actions as needed */}
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
  },
];
