import type { ColumnDef } from "@tanstack/react-table";import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ArrowUpDown, MoreHorizontal, Tag, Percent } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { Category } from "@/lib/api/categories/models";
import type { Brand } from "@/lib/api/brands/models";
import type { ItemMatrix } from "@/lib/api/items/matrices/models";
import { Badge } from "@/components/ui/badge";
import type { useNavigate } from "react-router";

export const getColumns = (
  brands: Brand[],
  categories: Category[],
  router:  ReturnType<typeof useNavigate>,
  storeCurrency: string = 'USD'
): ColumnDef<ItemMatrix>[] => [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="p-0 hover:bg-transparent font-bold"
          >
            Matrix Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        );
      },
      cell: ({ row }) => {
        const matrix = row.original;
        return (
          <Button
            variant="link"
            className="p-0 h-auto font-bold hover:no-underline"
            onClick={() => router(`/app/items/matrices/${matrix.id}`)}
          >
            {matrix.name}
          </Button>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "brand",
      header: "Brand",
      cell: ({ row }) => {
        const brand = brands.find(
          (brand) => brand.id === row.getValue("brand"),
        )?.name;
        return <div className="text-sm text-muted-foreground">{brand || '—'}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "category",
      header: "Category",
      cell: ({ row }) => {
        const category = categories.find(
          (category) => category.id === row.getValue("category"),
        )?.name;
        return <div className="text-sm text-muted-foreground">{category || '—'}</div>;
      },
      enableSorting: true,
    },
    {
      accessorKey: "has_discount",
      header: "Discount",
      cell: ({ row }) => {
        const hasDiscount = row.getValue("has_discount");
        const discount = row.original.discount;
        return (
          <div className="flex items-center gap-2">
            {hasDiscount ? (
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                <Percent className="h-3 w-3 mr-1" />
                {discount}%
              </Badge>
            ) : (
              <span className="text-sm text-muted-foreground">No discount</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "date_created",
      header: ({ column }) => (
        <Button
          variant="ghost"
          onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
        >
          Created Date
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("date_created"));
        return (
          <div className="text-sm">
            {date.toLocaleDateString('en-US', {
              year: 'numeric',
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "matrix_type",
      header: "Type",
      cell: ({ row }) => {
        const matrix = row.original;
        return (
          <Badge variant="outline" className="capitalize">
            {matrix.matrix_type}
          </Badge>
        );
      },
      enableSorting: true,
    },
    {
      accessorKey: "default_cost",
      header: "Cost",
      cell: ({ row }) => {
        const cost = row.getValue("default_cost") as number;
        return (
          <div className="font-medium">
            {cost.toLocaleString('en-US', { 
              style: 'currency', 
              currency: storeCurrency 
            })}
          </div>
        );
      },
      enableSorting: true,
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const matrix = row.original;
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem
                onClick={() => navigator.clipboard.writeText(matrix.id.toString())}
              >
                Copy Matrix ID
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => {
                  router(`/app/items/matrices/${matrix.id}`)
                }}
              >
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  router(`/app/items/matrices/${matrix.id}/edit`);
                }}
              >
                Edit Matrix
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
