//app/app/items/matrices/create/AttributesSection.tsx
import React, { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  InputGroup,
  InputGroupAddon,
  InputGroupButton,
  InputGroupInput,
} from "@/components/ui/input-group";
import { ChevronDownIcon } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import {
  type Attributes,
  type AttributeValues,
  type AttributePayload,
  type AttributeValuesSchema,
} from "@/lib/api/attributes/models";
import AttributeCard from "./AttributesCard";
import {
  createAttribute,
  fetchAttributes,
} from "@/lib/api/attributes/attributeService";
import Cookies from "js-cookie";
import AttributeConfig from "./page";

export interface AttributeConfig {
  attribute: Attributes;
  values: AttributeValues[];
  selectedValues: number[];
}

interface MatrixAttributesProps {
  attributes: AttributeConfig[];
  availableAttributes: Attributes[];
  onAddAttribute: (attributeId: number) => void;
  onRemoveAttribute: (attributeId: number) => void;
  onToggleValue: (attributeId: number, valueId: number) => void;
  onAddValue?: (attributeId: number, value: string) => void;
  onAddBulkValues?: (
    attributeId: number,
    values: AttributeValuesSchema,
  ) => Promise<void>;
  onRemoveValue?: (attributeId: number, valueId: number) => void;
  onCreateAttribute?: (name: string) => Promise<Attributes>;
  storeId: number;
}

const MatrixAttributes: React.FC<MatrixAttributesProps> = ({
  attributes,
  availableAttributes,
  onAddAttribute,
  onRemoveAttribute,
  onToggleValue,
  onAddValue,
  onAddBulkValues,
  onRemoveValue,
}) => {
  const [currentAttributeId, setCurrentAttributeId] = useState<number | null>(null);
  const [isNewAttributeDialogOpen, setIsNewAttributeDialogOpen] =
    useState(false);
  const [isValuesDialogOpen, setIsValuesDialogOpen] = useState(false);
  const [newAttributeName, setNewAttributeName] = useState("");
  const [isCreatingAttribute, setIsCreatingAttribute] = useState(false);

  const token = Cookies.get("auth_token");
  const store_id = Number(Cookies.get("active_store"));

  const filteredAttributes = useMemo(
    () =>
      availableAttributes.filter(
        (attr) =>
          !attributes.some(
            (existingAttr) => existingAttr.attribute.id === attr.id,
          ),
      ),
    [availableAttributes, attributes],
  );

  const totalCombinations = useMemo(
    () => attributes.reduce((acc, attr) => acc * attr.values.length, 1),
    [attributes],
  );

  const handleAttributeSelect = (value: string) => {
    if (value === "new") {
      setIsNewAttributeDialogOpen(true);
    } else {
      const numValue = parseInt(value, 10);
      setCurrentAttributeId(numValue);
      onAddAttribute(numValue);
      setIsValuesDialogOpen(true);
    }
  };

  const handleRemoveAttributeWithReset = (attributeId: number) => {
    onRemoveAttribute(attributeId);
    if (attributes.length <= 1) {
      setCurrentAttributeId(null);
    }
  };

  const handleCreateAttribute = async () => {
    try {
      setIsCreatingAttribute(true);

      const trimmedName = newAttributeName.trim();
      const payload: AttributePayload = {
        name: trimmedName,
      };
      await createAttribute(token!, store_id!, payload);
      const _attributes = await fetchAttributes(token!, store_id!);
      const newAttribute = _attributes.find(
        (attr) => attr.name === trimmedName,
      )!;

      setIsNewAttributeDialogOpen(false);
      setNewAttributeName("");
      setCurrentAttributeId(newAttribute.id);
      onAddAttribute(newAttribute.id);
      setIsValuesDialogOpen(true);
    } catch (error) {
      console.error("Failed to create attribute:", error);
    } finally {
      setIsCreatingAttribute(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium">Matrix Attributes</h3>
      </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <InputGroup>
            <InputGroupInput 
              placeholder="Add Attribute" 
              value={filteredAttributes.find(attr => attr.id === currentAttributeId)?.name || ''}
              readOnly
            />
            <InputGroupAddon align="inline-end">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <InputGroupButton variant="ghost" className="!pr-1.5 text-xs">
                    Select <ChevronDownIcon className="size-3" />
                  </InputGroupButton>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {filteredAttributes.map((attr) => (
                    <DropdownMenuItem 
                      key={attr.id} 
                      onClick={() => handleAttributeSelect(attr.id.toString())}
                    >
                      {attr.name}
                    </DropdownMenuItem>
                  ))}
                  <DropdownMenuItem 
                    onClick={() => handleAttributeSelect("new")} 
                    className="text-primary"
                  >
                    + Create New Attribute
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </InputGroupAddon>
          </InputGroup>
        </div>
      </div>

      <div className="space-y-4">
        {attributes.map((attr) => (
          <AttributeCard
            key={attr.attribute.id}
            attr={attr}
            onRemoveAttribute={handleRemoveAttributeWithReset}
            onToggleValue={onToggleValue}
            onAddValue={onAddValue}
            onRemoveValue={onRemoveValue}
            onAddBulkValues={onAddBulkValues}
          />
        ))}
      </div>

      {attributes.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Matrix Preview</h3>
          <div className="bg-gray-50 p-4 rounded-lg">
            <p className="text-sm text-gray-600">
              This matrix will create {totalCombinations} items with the
              following combinations:
            </p>
            <div className="mt-2">
              {attributes.map((attr) => (
                <div key={attr.attribute.id} className="text-sm">
                  <span className="font-medium">{attr.attribute.name}:</span>{" "}
                  {attr.values.map((v) => v.value).join(", ")}
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      <Dialog
        open={isNewAttributeDialogOpen}
        onOpenChange={setIsNewAttributeDialogOpen}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Attribute</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="attributeName">Attribute Name</Label>
              <Input
                id="attributeName"
                value={newAttributeName}
                onChange={(e) => setNewAttributeName(e.target.value)}
                placeholder="Enter attribute name"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsNewAttributeDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              onClick={handleCreateAttribute}
              disabled={!newAttributeName.trim() || isCreatingAttribute}
            >
              {isCreatingAttribute ? "Creating..." : "Create"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default MatrixAttributes;
