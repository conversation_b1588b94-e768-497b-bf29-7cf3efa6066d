// lib/hooks/useWebSocket.ts
import { useState, useEffect, useRef, useCallback } from 'react';
import Cookies from 'js-cookie';
import toast from 'react-hot-toast';

type WebSocketStatus = 'connecting' | 'open' | 'closed' | 'error';

interface WebSocketHook {
  connectionStatus: WebSocketStatus;
  lastMessage: any | null;
  sendMessage: (message: any) => void;
  reconnect: () => void;
}

export interface PaymentStatusMessage {
  type: 'payment_status';
  receiptId: string;
  status: 'completed' | 'failed' | 'pending';
  message: string;
  mpesaReceiptNumber?: string;
  timestamp?: string;
}

export interface SystemMessage {
  type: 'system';
  message: string;
  code?: number;
}

export type WebSocketMessage = PaymentStatusMessage | SystemMessage;

const MAX_RECONNECT_ATTEMPTS = 5;
const RECONNECT_INTERVAL_BASE = 1000; // 1 second base

export function useWebSocket(isEnabled: boolean = true): WebSocketHook {
  const [connectionStatus, setConnectionStatus] = useState<WebSocketStatus>('closed');
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const ws = useRef<WebSocket | null>(null);
  const reconnectAttempts = useRef(0);
  const reconnectTimer = useRef<number | null>(null);
  const isMounted = useRef(false);

  // const getWebSocketUrl = useCallback(() => {
  //   const token = Cookies.get('auth_token');
  //   if (!token) {
  //     throw new Error('No authentication token available');
  //   }
  //   return `ws://${BASE_DOMAIN}/messaging/ws?token=${encodeURIComponent(token)}`;
  // }, []);

  const getWebSocketUrl = useCallback(() => {
    const token = Cookies.get('auth_token');
    if (!token) throw new Error('No authentication token available');

    const baseUrl = import.meta.env.VITE_WS_URL;
    return `${baseUrl}?token=${encodeURIComponent(token)}`;
  }, []);

  const handleIncomingMessage = useCallback((data: string) => {
    try {
      const parsed: unknown = JSON.parse(data);

      // Type guard for PaymentStatusMessage
      if (typeof parsed === 'object' && parsed !== null && 'type' in parsed) {
        switch ((parsed as { type: string }).type) {
          case 'payment_status':
            setLastMessage(parsed as PaymentStatusMessage);
            break;
          case 'system':
            setLastMessage(parsed as SystemMessage);
            break;
          default:
            console.warn('Unknown message type:', (parsed as { type: string }).type);
        }
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }, []);
  8
  const cleanupWebSocket = useCallback(() => {
    if (ws.current) {
      ws.current.onopen = null;
      ws.current.onclose = null;
      ws.current.onerror = null;
      ws.current.onmessage = null;

      if (ws.current.readyState === WebSocket.OPEN) {
        ws.current.close(1000, 'Clean closure');
      }
      ws.current = null;
    }

    if (reconnectTimer.current) {
      clearTimeout(reconnectTimer.current);
      reconnectTimer.current = null;
    }
  }, []);

  const connect = useCallback(() => {
    if (!isEnabled || !isMounted.current) return;

    cleanupWebSocket();

    try {
      const url = getWebSocketUrl();
      setConnectionStatus('connecting');
      ws.current = new WebSocket(url);

      ws.current.onopen = () => {
        reconnectAttempts.current = 0;
        setConnectionStatus('open');
        console.log('WebSocket connected');
      };

      ws.current.onmessage = (event) => {
        handleIncomingMessage(event.data);
      };

      ws.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionStatus('error');
      };

      ws.current.onclose = (event) => {
        console.log(`WebSocket closed: ${event.code} - ${event.reason}`);
        setConnectionStatus('closed');

        // Don't reconnect if closed normally or hook is disabled
        if (event.code !== 1000 && isEnabled && isMounted.current) {
          scheduleReconnect();
        }
      };
    } catch (error) {
      console.error('WebSocket connection error:', error);
      setConnectionStatus('error');
      scheduleReconnect();
    }
  }, [isEnabled, getWebSocketUrl, handleIncomingMessage, cleanupWebSocket]);

  const scheduleReconnect = useCallback(() => {
    if (!isMounted.current || reconnectAttempts.current >= MAX_RECONNECT_ATTEMPTS) {
      return;
    }

    reconnectAttempts.current += 1;
    const delay = Math.min(
      RECONNECT_INTERVAL_BASE * Math.pow(2, reconnectAttempts.current),
      30000 // Max 30 seconds
    );

    console.log(`Scheduling reconnect attempt ${reconnectAttempts.current} in ${delay}ms`);

    reconnectTimer.current = setTimeout(() => {
      if (isMounted.current) {
        connect();
      }
    }, delay);
  }, [connect]);

  const sendMessage = useCallback((message: unknown) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      try {
        ws.current.send(JSON.stringify(message));
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        toast.error('Failed to send message');
      }
    } else {
      console.warn('Cannot send message - WebSocket not connected');
      toast.error('Connection not available');
    }
  }, []);

  const reconnect = useCallback(() => {
    reconnectAttempts.current = 0;
    connect();
  }, [connect]);

  useEffect(() => {
    isMounted.current = true;
    if (isEnabled) {
      connect();
    }

    return () => {
      isMounted.current = false;
      cleanupWebSocket();
    };
  }, [isEnabled, connect, cleanupWebSocket]);

  return {
    connectionStatus,
    lastMessage,
    sendMessage,
    reconnect
  };
}