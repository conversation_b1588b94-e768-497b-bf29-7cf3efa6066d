// app/app/integrations/mpesa/[mpesaId]/page.tsx
"use client";

import { useEffect, useState } from "react";
import Cookies from "js-cookie";
import {
  getMpesaIntegration,
  updateMpesaIntegration,
} from "@/lib/api/integrations/mpesa/service";
import { MpesaIntegrationForm } from "../../MpesaIntegrationForm";
import type { MpesaIntegrationFormValues } from "@/lib/api/integrations/mpesa/models";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Loader2 } from "lucide-react";
// import { inter } from "@/routes/ui/fonts";
import { useNavigate, useParams } from "react-router";


export default function EditMpesaIntegrationPage() {
  const router = useNavigate();
  const mpesaId = useParams().MpesaId ?? "0000-0000-0000-0000-000000000000";
  const [initialData, setInitialData] = useState<MpesaIntegrationFormValues | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchIntegrationData = async () => {
      const token = Cookies.get('auth_token');
      const store_id = Cookies.get("active_store");

      if (!token || !store_id) {
        throw new Error('Authentication token or store ID is missing.');
      }

      try {
        const data = await getMpesaIntegration();
        if (data) {
          setInitialData({
            business_short_code: data.business_short_code,
            consumer_key: data.consumer_key,
            consumer_secret: data.consumer_secret,
            pass_key: data.pass_key,
            store_id: data.store_id,
          });
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load M-Pesa integration");
      } finally {
        setIsLoading(false);
      }
    };

    fetchIntegrationData();
  }, [mpesaId]);

  const handleSubmit = async (data: MpesaIntegrationFormValues) => {
    const token = Cookies.get('auth_token');
    const store_id = Cookies.get("active_store");

    if (!token || !store_id) {
      throw new Error('Authentication token or store ID is missing.');
    }

    try {
      await updateMpesaIntegration(data);
      router('/app/integrations/mpesa');
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to update M-Pesa integration");
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto py-4 md:py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (!initialData) {
    return (
      <div className="container mx-auto py-4 md:py-8">
        <Alert>
          <AlertDescription>
            M-Pesa integration not found.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-4 md:py-8">
      <MpesaIntegrationForm
        initialData={initialData}
        onSubmit={handleSubmit}
        isEditing={true}
      />
    </div>
  );
}

