"use client";

import { useState, type ChangeEvent, type FormEvent, useEffect } from "react";
import {
  <PERSON>,
  Card<PERSON><PERSON>er,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { InputGroup, InputGroupInput, InputGroupAddon, InputGroupButton } from "@/components/ui/input-group";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { AlertError } from "@/components/errors";
import { AlertSuccess } from "@/components/success";
import type { Item } from "@/lib/api/items/models";
import type { ReturnSchema } from "@/lib/api/items/returns/models";
import { RefundStatus } from "@/lib/types/refund_status";
import type { Sale } from "@/lib/api/sales/models";
import type { Receipt, Item as ReceiptItem } from "@/lib/api/receipts/models";

interface ReturnFormProps {
  initialData?: Omit<ReturnSchema, "staff_id" | "sale_id">; // Omit staff_id from initial data
  onSubmit(data: ReturnSchema): Promise<void>;
  isEditing?: boolean;
  items: Item[];
  sales: Sale[];
  receipts: Receipt[];
  userId: number;
}

export function ReturnForm({
  initialData,
  onSubmit,
  isEditing = false,
  items,
  sales,
  receipts,
  userId,
}: ReturnFormProps) {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<Omit<ReturnSchema, "staff_id" | "sale_id">>(
    initialData || {
      item_id: 0,
      return_reason: "",
      receipt_id: 0,
      refund_status: RefundStatus.Pending,
      quantity: 1, // Default quantity
    }
  );
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);
  const [availableQuantity, setAvailableQuantity] = useState<number>(1);
  const [selectedReceipt, setSelectedReceipt] = useState<Receipt | null>(null);
  const [selectedItem, setSelectedItem] = useState<Item | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<string>('');

  useEffect(() => {
    if (formData.receipt_id && formData.item_id) {
      const receipt = receipts.find((r) => r.id === formData.receipt_id);
      const receiptItem = receipt?.items.find((item) => items.find(p => p.id === formData.item_id)?.name === item.name);

      if (receiptItem) {
        setAvailableQuantity(receiptItem.amount);
      } else {
        setAvailableQuantity(1); // Default if not found
      }
    } else {
      setAvailableQuantity(1);
    }
  }, [formData.receipt_id, formData.item_id, receipts, items]);

  const handleInputChange = (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSelectChange = (value: number | string, name: string) => {
    setFormData((prev) => ({
        ...prev,
        [name]: value,
    }));
};

  const handleQuantityChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    const parsedValue = parseInt(value, 10);

    if (!isNaN(parsedValue) && parsedValue >= 1 && parsedValue <= availableQuantity) {
      setFormData((prev) => ({
        ...prev,
        quantity: parsedValue,
      }));
    }
  };

  const handleFormSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const currentReceipt = receipts.find((receipt) => receipt.id === formData.receipt_id);
      const selectedSale = sales.find((sale) => currentReceipt && sale.receipt_id === currentReceipt.id);
      const sale_id = selectedSale?.id
      // Include the userId (staff_id) in the data sent to the API
      const returnData: ReturnSchema = {
        ...formData,
        staff_id: userId,
        sale_id: sale_id || 0,
      };
      await onSubmit(returnData);
      setSuccess(true);

      if (!isEditing) {
        setFormData({
          item_id: 0,
          return_reason: "",
          receipt_id: 0,
          refund_status: RefundStatus.Pending,
          quantity: 1,
        });
      }
    } catch (err) {
      const error = err as Error;
      setError(error.message || "An unexpected error occurred."); // Provide a default message
    } finally {
      setLoading(false);
    }
  };

  const currentReceiptForSale = receipts.find((receipt) => receipt.id === formData.receipt_id);
  // const selectedSale = sales.find((sale) => currentReceiptForSale && sale.receipt_id === currentReceiptForSale.id);

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        {error && <AlertError message={error} />}
        {success && (
          <AlertSuccess
            message={`Return ${isEditing ? "updated" : "created"} successfully!`}
          />
        )}
        <CardTitle>{isEditing ? "Edit Return" : "Product Return"}</CardTitle>
        <CardDescription>
          {isEditing
            ? "Edit the product return details"
            : "Process a product return request"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleFormSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">

            <div className="space-y-2">
              <Label htmlFor="receipt_id">Receipt ID *</Label>
              <InputGroup>
                <InputGroupInput 
                  placeholder="Select receipt" 
                  value={selectedReceipt ? `${selectedReceipt.receipt_number} - ${selectedReceipt.salesperson.split(' ')[0]} ${new Date(selectedReceipt.created_at).toLocaleString('en-US')}` : ''}
                  readOnly
                />
                <InputGroupAddon align="inline-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <InputGroupButton variant="ghost" className="text-xs">
                        Select <ChevronDown className="h-4 w-4" />
                      </InputGroupButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {receipts.map((receipt) => (
                        <DropdownMenuItem
                          key={receipt.id}
                          onClick={() => {
                            setSelectedReceipt(receipt);
                            handleSelectChange(receipt.id, "receipt_id");
                          }}
                        >
                          {receipt.receipt_number} - {receipt.salesperson.split(' ')[0]} {new Date(receipt.created_at).toLocaleString('en-US')}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </InputGroupAddon>
              </InputGroup>
            </div>
            <div className="space-y-2">
              <Label htmlFor="sale_id">Sale ID *</Label>
              <Input
                required
                id="sale_id"
                name="sale_id"
                value={sales.find((sale) => currentReceiptForSale && sale.receipt_id === currentReceiptForSale.id)?.id?.toString() || ""}
                disabled
                placeholder="Sale ID"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="item_id">Item *</Label>
              <InputGroup>
                <InputGroupInput 
                  placeholder="Select item" 
                  value={selectedItem ? `${selectedItem.name}` : ''}
                  readOnly
                />
                <InputGroupAddon align="inline-end">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <InputGroupButton variant="ghost" className="text-xs" disabled={!formData.receipt_id}>
                        Select <ChevronDown className="h-4 w-4" />
                      </InputGroupButton>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {selectedReceipt?.items
                        .sort((a, b) => a.name.localeCompare(b.name))
                        .map((receiptItem: ReceiptItem) => {
                          const product = items.find((product) => product.name === receiptItem.name);

                          if (!product) {
                            console.warn(`Product not found for receipt item: ${receiptItem.name}`);
                            return null;
                          }

                          return (
                            <DropdownMenuItem
                              key={product.id}
                              onClick={() => {
                                setSelectedItem(product);
                                handleSelectChange(product.id, "item_id");
                              }}
                            >
                              {receiptItem.name} - {receiptItem.amount}
                            </DropdownMenuItem>
                          );
                        })}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </InputGroupAddon>
              </InputGroup>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity * (Available: {availableQuantity})</Label>
              <Input
                type="number"
                id="quantity"
                name="quantity"
                value={formData.quantity?.toString() || "1"}
                onChange={handleQuantityChange}
                min="1"
                max={availableQuantity}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="return_reason">Return Reason</Label>
            <Textarea
              id="return_reason"
              name="return_reason"
              value={formData.return_reason}
              onChange={handleInputChange}
              placeholder="Enter reason for return"
              className="min-h-[80px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="refund_status">Refund Status</Label>
            <InputGroup>
              <InputGroupInput 
                placeholder="Select status" 
                value={selectedStatus || formData.refund_status}
                readOnly
              />
              <InputGroupAddon align="inline-end">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <InputGroupButton variant="ghost" className="text-xs">
                      Select <ChevronDown className="h-4 w-4" />
                    </InputGroupButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem
                      onClick={() => {
                        setSelectedStatus('Pending');
                        setFormData((prev) => ({ ...prev, refund_status: RefundStatus.Pending }));
                      }}
                    >
                      Pending
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setSelectedStatus('Processed');
                        setFormData((prev) => ({ ...prev, refund_status: RefundStatus.Processed }));
                      }}
                    >
                      Processed
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setSelectedStatus('Cancelled');
                        setFormData((prev) => ({ ...prev, refund_status: RefundStatus.Cancelled }));
                      }}
                    >
                      Cancelled
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => {
                        setSelectedStatus('Exchanged');
                        setFormData((prev) => ({ ...prev, refund_status: RefundStatus.Exchanged }));
                      }}
                    >
                      Exchanged
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </InputGroupAddon>
            </InputGroup>
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end space-x-4">
        <Button
          variant="outline"
          onClick={() =>
            setFormData({
              item_id: 0,
              return_reason: "",
              receipt_id: 0,
              refund_status: RefundStatus.Pending,
              quantity: 1,
            })
          }
        >
          Clear
        </Button>
        <Button onClick={handleFormSubmit} disabled={loading}>
          {loading ? "Processing..." : isEditing ? "Update Return" : "Submit Return"}
        </Button>
      </CardFooter>
    </Card>
  );
}