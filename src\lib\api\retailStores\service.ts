import { BASE_URL } from "@/routes/configs/constants";
import type { StoreSchema, Store } from "./models";
import Cookies from "js-cookie";

export async function getStores(authToken: string): Promise<Store[]> {
  try {
    const storeId = Number(Cookies.get("active_store"));
    const response = await fetch(`${BASE_URL}/admin/store/stores`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to fetch stores: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function getStore(
  storeId: number,
  authToken: string,
): Promise<Store> {
  try {
    const response = await fetch(`${BASE_URL}/admin/store/stores/${storeId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to fetch stores: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function createStore(
  store: StoreSchema,
  authToken: string,
): Promise<Store> {
  try {
    const storeId = Number(Cookies.get("active_store"));
    const response = await fetch(`${BASE_URL}/stores`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(store),
    });
    if (response.status === 201) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to create store: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function updateStore(
  storeId: number,
  store: StoreSchema,
  authToken: string,
): Promise<Store> {
  try {
    
    const response = await fetch(`${BASE_URL}/admin/store/stores/${storeId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
      body: JSON.stringify(store),
    });
    if (response.ok) {
      return await response.json();
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to update store: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (error) {
    console.error(error);
    throw error;
  }
}

export async function deleteStore(
  storeId: number,
  authToken: string,
): Promise<boolean> {
  try {
    const response = await fetch(`${BASE_URL}/admin/store/stores/${storeId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
        "X-Active-Store": storeId.toString(),
      },
    });
    if (response.ok) {
      return true;
    } else {
      const errorMessage = await response.json();
      throw new Error(
        `Failed to delete store: ${errorMessage.error || "Unknown error"}`,
      );
    }
  } catch (e) {
    console.error(e);
    return false;
  }
}
